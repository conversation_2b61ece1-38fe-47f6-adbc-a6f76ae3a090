{% extends "be/include/base.html" %}

{% set page = 'USER' %}

{% block extrahead %}
<title>Utenti</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/user-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_USER_DATA', '{{ routes("BE_USER_DATA") }}');
addRoute('BE_USER_OPERATE', '{{ routes("BE_USER_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Utenti</h5>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Selez.</th>
                    <th>Nome e Cognome</th>
                    <th>Email</th>
                    <th>Telefono</th>
                    <th>Tipo</th>
                    <th>Creazione</th>
                    <th>Ultima modifica</th>
                    <th class="text-center" style="width: 120px;">Azioni</th>
                    <th></th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}