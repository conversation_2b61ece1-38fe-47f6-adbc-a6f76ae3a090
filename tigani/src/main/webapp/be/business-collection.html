{% extends "be/include/base.html" %}

{% set page = 'BUSINESS' %}

{% block extrahead %}
<title>Attività</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/business-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_BUSINESS_DATA', '{{ routes("BE_BUSINESS_DATA") }}');
addRoute('BE_BUSINESS_OPERATE', '{{ routes("BE_BUSINESS_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Attività</h5>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Selez.</th>
                    <th>Nome</th>
                    <th>Utente</th>
                    <th>Categorie</th>
                    <th>Stato</th>
                    <th>In Evidenza</th>
                    <th>Creazione</th>
                    <th>Ultima modifica</th>
                    <th class="text-center" style="width: 120px;">Azioni</th>
                    <th></th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}