{% extends "be/include/base.html" %}

{% set area = 'CATEGORY' %}
{% set title = category is empty ? 'Nuova categoria ' : 'Modifica categoria' %}
{% set postUrl = routes('BE_CATEGORY') %}
{% if category.id is not empty %}
{% set postUrl = routes('BE_CATEGORY') + '?categoryId=' + category.id %}
{% endif %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<script src="{{ contextPath }}/be/js/pages/category.js?{{ buildNumber }}"></script>
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
addRoute('BE_CATEGORY', '{{ routes("BE_CATEGORY") }}');
addRoute('BE_IMAGE_SAVE', '{{ routes("BE_IMAGE_SAVE") }}');
addVariables('categoryId', '{{ category.id }}');
addVariables('imageIds', '{{ category.imageIds }}');
{% if parentId is not empty %}
addVariables('parentId', '{{ parentId }}');
addVariables('parentIdLanguage', '{{ parentIdLanguage }}');
{% endif %}
{% if requiredLanguage is not empty %}
addVariables('language', '{{ requiredLanguage }}');
{% endif %}
addVariables('imageId', '{{ category.imageId }}');
</script>
<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_CATEGORY_SAVE') %}
        {% if category.id is not empty %}
        {% set postUrl = routes('BE_CATEGORY_SAVE') + '?categoryId=' + category.id %}
        {% endif %}
        <form id="category" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">
                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Lingua: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select name="language" class="form-control select-language" data-minimum-results-for-search="Infinity" {{ category.id is empty and requiredLanguage is empty ? '' : 'disabled' }}>
                                    {% for language in availableLanguages %}
                                    <option value="{{ language }}" {{ (category.id is not empty and category.language == language) or (requiredLanguage is not empty and requiredLanguage == language) ? 'selected' : '' }}>{{ language }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">Il progetto sarà pubblicato nella rispettiva versione del sito sulla base della lingua scelta. Successivamente potrai creare delle copie tradotte della stessa galleria per le altre lingue.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Titolo: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="title" name="title" type="text" class="form-control maxlength" placeholder="Titolo" value="{{ category.title }}" {{ disabled }} maxlength="300" required>
                                <div class="form-text text-muted">Il titolo sarà l'H1 (intestazione principale) della tua pagina.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Descrizione: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <textarea id="description" name="description" rows="20" cols="20" class="form-control ckeditor" placeholder="Descrivi il progetto" {{ disabled }} required>{{ category.description }}</textarea>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Ordinamento:</label>
                            <div class="col-lg-10">
                                <input id="order" name="order" type="text" class="form-control" placeholder="1" value="{{ category.order }}" {{ disabled }}>
                            </div>
                        </div>
			<div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Immagine:</label>
                            <div class="col-lg-10">
                                <input id="socialShare" name="socialShare" type="file" class="filepond" data-allow-reorder="true" data-max-file-size="3MB" data-max-files="1">
                                <div class="form-text text-muted">Scegli un'immagine di alta qualità con dimensioni di almeno 1.200 x 630 pixel.</div>
                            </div>
                        </div>
                    </fieldset>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="btn-group w-auto mt-sm-0">
                        <button class="btn btn-danger w-100 w-sm-auto"><i class="ph-trash me-sm-2"></i><span class="d-none d-sm-block">Archivia</span></button>
                        <button class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
                            <a href="#" class="dropdown-item"><i class="ph-x me-2"></i> Elimina</a>
                        </div>
                    </div>
                    <div class="hstack gap-2 mt-0">
                        <a href="{{ routes('BE_CATEGORY_COLLECTION') }}" class="btn btn-light w-auto btn-cancel">
                            <i class="ph-arrow-u-up-left me-sm-2"></i>
                            <span class="d-none d-sm-block">Annulla</span>
                        </a>
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

{% endblock %}

{% block sidebar %}
    {% include "be/include/snippets/sidebar/sidebar-category.html" %}
{% endblock %}