{% extends "be/include/base.html" %}

{% set page = 'PROJECT' %}
{% set title = '<PERSON><PERSON>ti' %}

{% block extrahead %}
<title><PERSON><PERSON>ti</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/project-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_PROJECT_DATA', '{{ routes("BE_PROJECT_DATA") }}');
addRoute('BE_PROJECT_OPERATE', '{{ routes("BE_PROJECT_OPERATE") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">{{ title }}</h5>
            <div class="ms-sm-auto my-sm-auto">
                <a href="{{ routes('BE_PROJECT') }}" class="btn btn-primary w-100">
                    <i class="ph-plus me-2"></i>
                    Nuovo progetto
                </a>
            </div>
        </div>

        <table class="table datatable">
            <thead>
                <tr>                    
                    <th>Selez.</th>
                    <th>Titolo</th>
                    <th>Categoria</th>
                    <th>Stato</th>
                    <th>Data pubblicazione</th>
                    <th>Lingue disponibili</th>
                    <th>Creazione</th>
                    <th>Ultima modifica</th>
                    <th class="text-center" style="width: 120px;">Azioni</th>
                    <th></th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}