{% extends "be/include/base.html" %}

{% set area = 'SCRIPT' %}
{% set title = photo is empty ? 'Nuovo script ' : 'Modifica script' %}
{% set postUrl = routes('BE_SCRIPT') %}
{% if script.id is not empty %}
{% set postUrl = routes('BE_SCRIPT') + '?scriptId=' + script.id %}
{% endif %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<script src="{{ contextPath }}/be/js/pages/script.js?{{ buildNumber }}"></script>
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
addRoute('BE_SCRIPT', '{{ routes("BE_SCRIPT") }}');
addRoute('BE_IMAGE_SAVE', '{{ routes("BE_IMAGE_SAVE") }}');
addVariables('scriptId', '{{ script.id }}');
{% if parentId is not empty %}
addVariables('parentId', '{{ parentId }}');
addVariables('parentIdLanguage', '{{ parentIdLanguage }}');
{% endif %}
{% if requiredLanguage is not empty %}
addVariables('language', '{{ requiredLanguage }}');
{% endif %}
</script>
<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_SCRIPT_SAVE') %}
        {% if script.id is not empty %}
        {% set postUrl = routes('BE_SCRIPT_SAVE') + '?scriptId=' + script.id %}
        {% endif %}
        <form id="photo" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">
                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Lingua: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select name="language" class="form-control select-language" data-minimum-results-for-search="Infinity" {{ script.id is empty and requiredLanguage is empty ? '' : 'disabled' }}>
                                    {% for language in availableLanguages %}
                                    <option value="{{ language }}" {{ (script.id is not empty and script.language == language) or (requiredLanguage is not empty and requiredLanguage == language) ? 'selected' : '' }}>{{ language }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">La galleria sarà pubblicato nella rispettiva versione del sito sulla base della lingua scelta. Successivamente potrai creare delle copie tradotte della stessa galleria per le altre lingue.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Ogetto: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="object" name="object" type="text" class="form-control maxlength" placeholder="Oggetto" value="{{ script.object }}" {{ disabled }} maxlength="300" required>
                                <div class="form-text text-muted">L'oggetto che verrà inserito nella mail.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Corpo del messaggio: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <textarea id="description" name="description" rows="20" cols="20" class="form-control ckeditor" placeholder="Descrivi la galleria" {{ disabled }} required>{{ script.description }}</textarea>
                            </div>
                        </div>

                    </fieldset>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="btn-group w-auto mt-sm-0">
                        <button class="btn btn-danger w-100 w-sm-auto"><i class="ph-trash me-sm-2"></i><span class="d-none d-sm-block">Archivia</span></button>
                        <button class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
                            <a href="#" class="dropdown-item"><i class="ph-x me-2"></i> Elimina</a>                            
                        </div>
                    </div>
                    <div class="hstack gap-2 mt-0">
                        <a href="{{ routes('BE_SCRIPT_COLLECTION') }}" class="btn btn-light w-auto btn-cancel">
                            <i class="ph-arrow-u-up-left me-sm-2"></i>
                            <span class="d-none d-sm-block">Annulla</span>
                        </a>
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

{% endblock %}

{% block sidebar %}
    {% include "be/include/snippets/sidebar/sidebar-script.html" %}
{% endblock %}