{% extends "be/include/base.html" %}

{% set area = 'SETTINGS' %}
{% set page = 'SETTINGS_LANGUAGES' %}
{% set title = 'Impostazioni' %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->    
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
<script src="{{ contextPath }}/be/js/pages/settings-languages.js?{{ buildNumber }}"></script>        
<!-- /page script -->
{% endblock %}

{% block content %}

<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_SETTINGS_SAVE') %}
        <form class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">Lingue attive</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">                    

                    <div class="row mb-3">
                        <label class="col-lg-2 col-form-label">Lingue: <span class="text-danger">*</span></label>
                        <div class="col-lg-10">
                            <select name="languages" multiple class="form-control select-language" data-minimum-results-for-search="Infinity">
                                {% for language in allLanguages %}
                                <option value="{{ language }}" {{ (settings is not empty and settings.availableLanguages is not empty and settings.availableLanguages contains language) ? 'selected' : '' }}>{{ language }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text text-muted">Lingue disponibili in fase di inserimento di entità (es. articoli, prodotti...). Le lingue disponibili non sono automaticamente visibili nel sito.</div>
                        </div>
                    </div>

                    <div class="row mb-3">
                        <label class="col-lg-2 col-form-label">Lingua default: <span class="text-danger">*</span></label>
                        <div class="col-lg-10">
                            <select name="defaultLanguage" class="form-control select-language" data-minimum-results-for-search="Infinity">
                                {% for language in availableLanguages %}
                                <option value="{{ language }}" {{ (requiredLanguage is not empty and requiredLanguage == language) ? 'selected' : '' }}>{{ language }}</option>
                                {% endfor %}
                            </select>
                            <div class="form-text text-muted">La lingua di default del sito e la lingua di default in fase di caricamento di una nuova entità (es. articolo, prodotto...).</div>
                        </div>
                    </div>
                </div>
                <div class="card-footer d-flex justify-content-end align-items-center py-sm-2">                    
                    <div class="hstack gap-2 mt-0">                        
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
        <form id="visibleLanguages" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">Lingue visibili nel sito (attivare prima le lingue)</h5>
                    <div class="ms-sm-auto my-sm-auto">

                    </div>
                </div>

                <div class="card-body">                    
                    {% if availableLanguages is not empty %}
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Lingue visibili nel sito: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select name="visibleLanguages" multiple class="form-control select-language" data-minimum-results-for-search="Infinity">
                                    {% for language in availableLanguages %}
                                        <option value="{{ language }}" {{ (settings is not empty and settings.visibleLanguages is not empty and settings.visibleLanguages contains language) ? 'selected' : '' }}>{{ language }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">Lingue visibili nel sito</div>
                            </div>
                        </div>
                    {% endif %}
                </div>
                <div class="card-footer d-flex justify-content-end align-items-center py-sm-2">                    
                    <div class="hstack gap-2 mt-0">                        
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>


{% endblock %}
