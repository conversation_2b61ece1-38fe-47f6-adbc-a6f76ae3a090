{% extends "be/include/base.html" %}

{% set area = 'MAIL' %}
{% set page = 'CONTACTS' %}

{% block extrahead %}
<title>Contatti</title>
{% include "be/include/snippets/plugins/datatable.html" %}
<script src="{{ contextPath }}/be/js/pages/contact-collection.js?{{ buildNumber }}"></script>        
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
addRoute('BE_CONTACT_DATA', '{{ routes("BE_CONTACT_DATA") }}');
</script>

<!-- Content area -->
<div class="content container pt-0">

    <!-- Checkbox selection -->
    <div class="card">
        <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
            <h5 class="py-sm-3 mb-sm-0">Lista di Contatti</h5>
        </div>

        <table class="table datatable">
            <thead>
                <tr>
                    <th>Template</th>
                    <th>Nome</th>
                    <th>Cognome</th>
                    <th>Rag. sociale</th>
                    <th>Email</th>
                    <th>Oggetto</th>
                    <th>Data</th>
                    <th class="text-center">Actions</th>
                </tr>
            </thead>
        </table>
    </div>
    <!-- /checkbox selection -->
</div>
<!-- /content area -->

{% endblock %}