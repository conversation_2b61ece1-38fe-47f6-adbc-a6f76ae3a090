{% extends "be/include/base.html" %}

{% set page = 'PROJECT' %}
{% set area = 'PROJECT' %}
{% set title = project is empty ? 'Nuovo progetto' : 'Modifica progetto' %}
{% set postUrl = routes('BE_PROJECT') %}
{% if project.id is not empty %}
{% set postUrl = routes('BE_PROJECT') + '?projectId=' + project.id %}
{% endif %}

{% block extrahead %}
<title>{{ title }}</title>

<!-- Page script -->
{% include "be/include/snippets/plugins/ckeditor.html" %}
{% include "be/include/snippets/plugins/select2.html" %}
{% include "be/include/snippets/plugins/daterangepicker.html" %}
{% include "be/include/snippets/plugins/filepond.html" %}
{% include "be/include/snippets/plugins/validate.html" %}
{% include "be/include/snippets/plugins/maxlength.html" %}
{% include "be/include/snippets/plugins/slugify.html" %}
<script src="{{ contextPath }}/be/js/pages/project.js?{{ buildNumber }}"></script>
<!-- /page script -->
{% endblock %}

{% block content %}
<script class="reload-script-on-load">
    addRoute('BE_IMAGE', '{{ routes("BE_IMAGE") }}');
    addRoute('BE_PROJECT', '{{ routes("BE_PROJECT") }}');
    addRoute('BE_IMAGE_SAVE', '{{ routes("BE_IMAGE_SAVE") }}');
    addVariables('projectId', '{{ project.id }}');
    addVariables('imageIds', '{{ project.imageIds }}');
    {% if parentId is not empty %}
        addVariables('parentId', '{{ parentId }}');
        addVariables('parentIdLanguage', '{{ parentIdLanguage }}');
    {% endif %}
    {% if requiredLanguage is not empty %}
        addVariables('language', '{{ requiredLanguage }}');
    {% endif %}
    addVariables('imageId', '{{ project.imageId }}');
</script>
<div class="row justify-content-center">
    <div class="col-xxl-10">
        {% set postUrl = routes('BE_PROJECT_SAVE') %}
        {% if project.id is not empty %}
        {% set postUrl = routes('BE_PROJECT_SAVE') + '?projectId=' + project.id %}
        {% endif %}
        <form id="project" class="form-validate-jquery" method="POST" action="{{ postUrl }}" enctype="multipart/form-data">

            <div class="card">
                <div class="card-header d-sm-flex align-items-sm-center py-sm-0">
                    <h5 class="py-sm-3 mb-sm-auto">{{ title }}</h5>
                    <div class="ms-sm-auto my-sm-auto">
                        
                    </div>
                </div>

                <div class="card-body">
                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Dati principali</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Lingua: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select name="language" class="form-control select-language" data-minimum-results-for-search="Infinity" {{ project.id is empty and requiredLanguage is empty ? '' : 'disabled' }}>
                                    {% for language in availableLanguages %}
                                        <option value="{{ language }}" {{ (project.id is not empty and project.language == language) or (requiredLanguage is not empty and requiredLanguage == language) ? 'selected' : '' }}>{{ language }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">Il progetto sarà pubblicato nella rispettiva versione del sito sulla base della lingua scelta. Successivamente potrai creare delle copie tradotte dello stesso progetto per le altre lingue.</div>
                            </div>
                        </div>
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Titolo: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <input id="title" name="title" type="text" class="form-control maxlength" placeholder="Titolo" value="{{ project.title }}" {{ disabled }} maxlength="300" required>
                                <div class="form-text text-muted">Il titolo sarà l'H1 (intestazione principale) della tua pagina.</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Descrizione: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <textarea id="description" name="description" rows="20" cols="20" class="form-control ckeditor" placeholder="Descrivi il progetto" {{ disabled }} required>{{ project.description }}</textarea>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Categoria: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <select id="category" name="category" class="form-control select" multiple="multiple" data-tags="true" data-maximum-selection-length="1" data-placeholder="Categoria" {{ disabled }} required>
                                    {% set categoryList = categorylist('Project', requiredLanguage) %}
                                    {% for category in categoryList %}
                                    <option value="{{ category }}" {{ project.category == category ? 'selected' : '' }}>{{ category }}</option>
                                    {% endfor %}
                                </select>
                                <div class="form-text text-muted">Puoi scegliere fra una categoria già utilizzata o crearne una in tempo reale scrivendone il nome e premendo il tasto Invio.</div>
                            </div>
                        </div>                    

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Immagini:</label>
                            <div class="col-lg-10">                                
                                <input id="socialShare" name="socialShare" type="file" class="filepond" multiple data-allow-reorder="true" data-max-file-size="3MB" data-max-files="10">                                
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Tags:</label>
                            <div class="col-lg-10">
                                <div class="w-100">
                                    <select id="tags" name="tags" class="form-control select" multiple="multiple" data-tags="true" data-maximum-selection-length="10" data-placeholder="Tags" {{ disabled }}>
                                        {% set tagList = taglist('Project', requiredLanguage) %}
                                        {% for tag in tagList %}
                                            <option value="{{ tag }}" {{ project.tags contains tag ? 'selected' : '' }}>{{ tag }}</option>
                                        {% endfor %}
                                    </select>                                    
                                    <div class="form-text text-muted">Utilizza i tag (10 al massimo) per categorizzare il tuo progetto e rendilo più facilmente rintracciabile.</div>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">URL pubblico: <span class="text-danger">*</span></label>
                            <div class="col-lg-10">
                                <div class="input-group">
                                    <span class="input-group-text">/</span>
                                    <input id="identifier" name="identifier" type="text" class="form-control maxlength identifier" placeholder="URL pubblico" value="{{ project.identifier }}" maxlength="300" {{ disabled }} required>
                                </div>
                                <div class="form-text text-muted">Questo sarà il tuo link pubblico: <span class="text-primary">https://www.tigani.com/it/progetti/<span class="fw-bold" id="identifier-preview">{{ project.id is empty ? '&lt;scrivi un titolo&gt;' : project.identifier }}</span></span>.</div>
                            </div>
                        </div>

                    </fieldset>

                    <fieldset>

                        <legend class="fs-base fw-bold border-bottom pb-2 mb-3">Pubblicazione</legend>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Stato:</label>
                            <div class="col-lg-10">
                                <label for="status-draft" class="radio-card">
                                    <input type="radio" name="status" id="status-draft" value="draft" {{ (project.status is empty or project.status == 'draft') ? 'checked' : '' }}>
                                    <div class="card-content-wrapper">
                                        <span class="check-icon me-2"></span>
                                        <div class="card-content align-self-start lh-1">                                    
                                            <div class="fw-semibold mb-1"><i class="ph-eye-slash me-1"></i> Non visibile</div>
                                            <span class="form-text text-muted">Progetto in bozza non pubblicato</span>
                                        </div>
                                    </div>
                                </label>
                                <label for="status-published" class="radio-card">
                                    <input type="radio" name="status" id="status-published" value="published" {{ project.status == 'published' ? 'checked' : '' }}>
                                    <div class="card-content-wrapper">
                                        <span class="check-icon me-2"></span>
                                        <div class="card-content align-self-start lh-1">                                    
                                            <div class="fw-semibold mb-1"><i class="ph-eye me-1"></i>Visibile</div>
                                            <span class="form-text text-muted">Progetto pubblicato o programmato</span>
                                        </div>
                                    </div>
                                </label>
                            </div>
                        </div>                                                                       
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Data:</label>
                            <div class="col-lg-10">
                                <div class="input-group">
                                    <span class="input-group-text"><i class="ph-calendar"></i></span>
                                    <input type="text" name="publication" class="form-control daterange-single" value="{{ project.publication | date('dd/MM/yyyy') }}">
                                </div>
                                <div class="alert alert-primary alert-dismissible fade show mt-2 mb-0">                                    
                                    <i class="ph-calendar me-2"></i> <span class="fw-semibold">Hai inserito una data di pubblicazione nel futuro!</span> Il progetto sarà visibile online a chiunque abbia il link a partire da quella data. Nel mentre è visibile esclusivamente agli amministratori del sito.
                                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Scelto dalla redazione:</label>
                            <div class="col-lg-10 align-self-center">
                                <div class="form-check form-switch">
                                    <input type="checkbox" name="editorChoice" id="editorChoice" class="form-check-input" {{ project.editorChoice is not empty and project.editorChoice ? 'checked' : '' }}>                            
                                </div>                        
                            </div>                                                               
                        </div>

                        
                        {#
                        
                        <div class="row mb-3">
                            <label class="col-lg-2 col-form-label">Stato:</label>
                            <div class="col-lg-10">
                                <select class="form-control select-icons" name="status">
                                    <option value="published" data-icon="eye" {{ project.status == 'published' ? 'selected' : '' }}>Pubblicato</option>
                                    <option value="planned" data-icon="clock" {{ project.status == 'planned' ? 'selected' : '' }}>Pianificato</option>
                                    <option value="draft" data-icon="hourglass" {{ project.status == 'draft' ? 'selected' : '' }}>Bozza</option>
                                </select>
                            </div>
                        </div>
                        
                        #}
                    </fieldset>
                </div>
                <div class="card-footer d-flex justify-content-between align-items-center py-sm-2">
                    <div class="btn-group w-auto mt-sm-0">
                        <button class="btn btn-danger w-100 w-sm-auto"><i class="ph-trash me-sm-2"></i><span class="d-none d-sm-block">Archivia</span></button>
                        <button class="btn btn-danger dropdown-toggle dropdown-toggle-split" data-bs-toggle="dropdown" aria-expanded="false"></button>
                        <div class="dropdown-menu dropdown-menu-end" style="">
                            <a href="#" class="dropdown-item"><i class="ph-x me-2"></i> Elimina</a>                            
                        </div>
                    </div>
                    <div class="hstack gap-2 mt-0">
                        <a href="{{ routes('BE_PROJECT_COLLECTION_BASE') }}" class="btn btn-light w-auto btn-cancel">
                            <i class="ph-arrow-u-up-left me-sm-2"></i>
                            <span class="d-none d-sm-block">Annulla</span>
                        </a>
                        <button type="submit" class="btn btn-primary w-100 w-auto">
                            <i class="ph-check me-2"></i>
                            Salva
                        </button>
                    </div>
                </div>
            </div>

        </form>
    </div>
</div>

{% endblock %}

{% block sidebar %}
    {% include "be/include/snippets/sidebar/sidebar-project.html" %}
{% endblock %}