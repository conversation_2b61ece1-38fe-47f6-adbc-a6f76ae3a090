package controller;

import com.mongodb.client.model.Filters;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.BusinessDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.ProfileType;
import enums.StatusType;
import extensions.LabelsFunction;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Business;
import pojo.BusinessPreferred;
import pojo.QueryOptions;
import pojo.User;
import spark.*;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

import java.util.*;

import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.or;

/**
 *
 * <AUTHOR>
 */
public class ResultController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ResultController.class.getName());

    public static TemplateViewRoute results = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        if (request.queryParams().contains("city")) {
            String city = request.queryParams("city");
            attributes.put("city", city);
        }

        if (request.queryParams().contains("category")) {
            String category = request.queryParams("category");
            attributes.put("category", category);
        }

        // lista disinct delle città
        List<String> cities = BusinessDao.getDistinctCities();
        attributes.put("cities", cities);

        return Core.render(Pages.RESULTS, attributes, request);
    };

    public static TemplateViewRoute results_data = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes);

        // Parse request parameters
        Map<String, String> params = new HashMap<>();
        Map<String, UploadedFile> files = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);

        // Build filters list
        List<Bson> filters = new ArrayList<>();

        // Always filter by confirmed status
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.CONFIRMED.name().toLowerCase()));

        List<Bson> andFilters = new ArrayList<>();
        List<Bson> orFilters = new ArrayList<>();
        for (String filterName : params.keySet()) {
            if (!StringUtils.equals(filterName, "page")) {
                if (StringUtils.equalsIgnoreCase(filterName, "diving")) {
                    orFilters.add(DaoFilters.getFilter("diving", DaoFiltersOperation.EQ, BooleanUtils.toBoolean(params.get(filterName))));
                } else if (StringUtils.equalsIgnoreCase(filterName, "freedivingApnea")) {
                    orFilters.add(DaoFilters.getFilter("freedivingApnea", DaoFiltersOperation.EQ, BooleanUtils.toBoolean(params.get(filterName))));
                } else if (StringUtils.equalsIgnoreCase(filterName, "experience")) {
                    orFilters.add(DaoFilters.getFilter("experience", DaoFiltersOperation.EQ, BooleanUtils.toBoolean(params.get(filterName))));
                } else if (StringUtils.equalsIgnoreCase(filterName, "sport")) {
                    orFilters.add(DaoFilters.getFilter("sport", DaoFiltersOperation.EQ, BooleanUtils.toBoolean(params.get(filterName))));
                } else {
                    String value = params.get(filterName);
                    Object filterValue;

                    if (BooleanUtils.toBooleanObject(value) != null) {
                        filterValue = BooleanUtils.toBoolean(value);
                    } else if (value.contains("|")) {
                        filterValue = Arrays.asList(value.split("\\|"));
                    } else {
                        filterValue = value;
                    }

                    andFilters.add(DaoFilters.getFilter(filterName, DaoFiltersOperation.EQ, filterValue));
                }
            }
        }

        if (!andFilters.isEmpty()) {
            filters.add(and(or(andFilters)));
        }
        if (!orFilters.isEmpty()) {
            filters.add(and(or(orFilters)));
        }

        // Pagination parameters
        int page = 1;
        int limit = 12; // Default items per page

        try {
            if (StringUtils.isNotBlank(params.get("page"))) {
                page = Integer.parseInt(params.get("page"));
            }
            if (StringUtils.isNotBlank(params.get("limit"))) {
                limit = Integer.parseInt(params.get("limit"));
            }
        } catch (NumberFormatException ex) {
            LOGGER.warn("Invalid pagination parameters", ex);
        }

        int skip = (page - 1) * limit;

        // Sorting parameters
        String orderBy = StringUtils.defaultIfBlank(params.get("orderBy"), "creation");
        String orderType = StringUtils.defaultIfBlank(params.get("orderType"), "desc");

        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, skip, limit, orderBy, orderType);

        try {
            // Execute query
            List<Business> businesses = BaseDao.getDocumentsByFilters(Business.class, queryOptions);
            long totalCount = BaseDao.countDocumentsByFilters(Business.class, queryOptions);

            // Calculate pagination metadata
            int totalPages = (int) Math.ceil((double) totalCount / limit);
            boolean showPagination = totalCount > limit; // Show pagination if more than 12 items

            // Calculate page range for pagination display
            int startPage = 1;
            int endPage = totalPages;

            // For large datasets, show a range of pages around current page
            if (totalPages > 7) {
                if (page <= 4) {
                    // Show first 5 pages + ellipsis + last page
                    endPage = 5;
                } else if (page >= totalPages - 3) {
                    // Show first page + ellipsis + last 5 pages
                    startPage = totalPages - 4;
                } else {
                    // Show first page + ellipsis + current page range + ellipsis + last page
                    startPage = page - 2;
                    endPage = page + 2;
                }
            }

            // loadmore (keeping for backward compatibility)
            boolean loadmore = false;
            if (totalCount > skip + limit) {
                loadmore = true;
            }

            // Add results to attributes
            attributes.put("businesses", businesses);
            attributes.put("totalCount", totalCount);
            attributes.put("currentPage", page);
            attributes.put("limit", limit);
            attributes.put("loadmore", loadmore);

            // Add pagination metadata
            attributes.put("totalPages", totalPages);
            attributes.put("showPagination", showPagination);
            attributes.put("startPage", startPage);
            attributes.put("endPage", endPage);
            attributes.put("hasPrevious", page > 1);
            attributes.put("hasNext", page < totalPages);
            attributes.put("showFirstPage", startPage > 1);
            attributes.put("showLastPage", endPage < totalPages);
            attributes.put("showStartEllipsis", startPage > 2);
            attributes.put("showEndEllipsis", endPage < totalPages - 1);

            // business preferiti
            if (user != null) {
                filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
                queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
                List<BusinessPreferred> businessPreferreds = BaseDao.getDocumentsByFilters(BusinessPreferred.class, queryOptions, false);
                // Map them for id
                Map<ObjectId, BusinessPreferred> businessPreferredMap = new HashMap<>();
                for (BusinessPreferred businessPreferred : businessPreferreds) {
                    businessPreferredMap.put(businessPreferred.getBusinessId(), businessPreferred);
                }
                attributes.put("businessPreferreds", businessPreferredMap);
            }

            // Add filter parameters back to attributes for form state
            attributes.put("filters", params);
        } catch (Exception ex) {
            LOGGER.error("Error executing business query", ex);
            attributes.put("businesses", new ArrayList<Business>());
            attributes.put("totalCount", 0);
            attributes.put("currentPage", 1);
            attributes.put("totalPages", 0);
            attributes.put("showPagination", false);
            attributes.put("error", "Unable to load results. Please try again.");
        }

        return Core.render(Pages.RESULTS_DATA, attributes, request);
    };
}