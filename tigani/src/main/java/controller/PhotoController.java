package controller;

import com.github.slugify.Slugify;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.LogType;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Photo;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.Defaults;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class PhotoController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PhotoController.class.getName());

    public static TemplateViewRoute be_photo_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_PHOTO_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_photo = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
            attributes.put("requiredLanguage", language);
        }
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("photoId"));
        if (oid != null) {
            Photo loadedPhoto = BaseDao.getDocumentById(oid, Photo.class, language);
            attributes.put("photo", loadedPhoto);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Photo loadedPhoto = BaseDao.getDocumentByParentId(parentId, Photo.class, language);
                if (loadedPhoto != null) {
                    attributes.put("photo", loadedPhoto);
                }
            }
        }

        return Core.render(Pages.BE_PHOTO, attributes, request);
    };

    public static Route be_photo_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Photo> photoListToCheck = new ArrayList<>();
        Map<String, Photo> parentPhotoMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Photo> loadedPhotos;
            if (loadArchived) {
                loadedPhotos = BaseDao.getArchivedDocuments(Photo.class, languageToLoad);
            } else {
                loadedPhotos = BaseDao.getDocuments(Photo.class, languageToLoad);
            }
            if (loadedPhotos != null && !loadedPhotos.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Photo photo : loadedPhotos) {
                        parentPhotoMap.put(photo.getParentId(), photo);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    photoListToCheck.addAll(loadedPhotos);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!photoListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Photo photo : photoListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, photo.getLanguage())) {
                            if (!parentPhotoMap.containsKey(photo.getParentId())) {
                                parentPhotoMap.put(photo.getParentId(), photo);
                            }
                        }
                    }
                }
            }
        }
        List<Photo> photoList = new ArrayList<>(parentPhotoMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!photoList.isEmpty()) {
            for (Photo photo : photoList) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a language='").append(photo.getLanguage()).append("' photoId='").append(photo.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_PHOTO + "?photoId=").append(photo.getId()).append("&language=").append(photo.getLanguage()).append("'>").append(photo.getTitle()).append("</a>\",");
                json.append("\"").append(photo.getCategory()).append("\",");
                json.append("\"").append(StringUtils.join(photo.getAvailableLanguages(), ", ")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(photo.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_photo_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        if (language == null) {
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("photoId"));
        Photo newPhoto;
        if (oid != null) {
            newPhoto = BaseDao.getDocumentById(oid, Photo.class, language);
            if (StringUtils.isBlank(newPhoto.getCategoryIdentifier())) {
                Slugify slg = new Slugify();
                String categoryIdentifier = newPhoto.getCategory();
                newPhoto.setCategoryIdentifier(slg.slugify(categoryIdentifier));
            }            
            RequestUtils.mergeFromParams(params, newPhoto);
        } else {
            newPhoto = RequestUtils.createFromParams(params, Photo.class);
            if (StringUtils.isBlank(newPhoto.getIdentifier())) {
                newPhoto.setIdentifier(newPhoto.getTitle() + "-" + RoutesUtils.generateIdentifier());
            }
            if (StringUtils.isBlank(newPhoto.getCategoryIdentifier())) {
                Slugify slg = new Slugify();
                String categoryIdentifier = newPhoto.getCategory();
                newPhoto.setCategoryIdentifier(slg.slugify(categoryIdentifier));
            }
        }

        if (newPhoto != null) {
            Photo loadedByIdentifier = BaseDao.getDocumentByIdentifier(newPhoto.getIdentifier(), Photo.class, language);
            Photo loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Photo.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Photo photoToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Photo.class, languageToUpdate);
                        if (photoToUpdate != null) {
                            photoToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(photoToUpdate, photoToUpdate.getLanguage());
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage());
            }

            if (loadedByIdentifier != null && loadedByIdentifier.getId() != null) {
                if (newPhoto.getId() == null || !loadedByIdentifier.getId().equals(newPhoto.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Photo with the same identifier already exists");
                }
            }

            newPhoto.setLanguage(language);
            if (loadedByParentId != null) {
                newPhoto.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newPhoto.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newPhoto.getParentId())) {
                newPhoto.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newPhoto.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newPhoto, language);
                newPhoto.setId(oid);

                BaseDao.insertLog(user, newPhoto, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newPhoto, language);
                BaseDao.insertLog(user, newPhoto, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(photoFromQuery, newPhoto);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newPhoto, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newPhoto, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newPhoto != null ? newPhoto.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_photo_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String photos = params.get("photoIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(photos) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> photoIds = new HashMap<>();
            if (StringUtils.contains(photos, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(photos, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        photoIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(photos, "|"));
                if (parts.size() == 2) {
                    photoIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!photoIds.isEmpty()) {
                for (ObjectId photoId : photoIds.keySet()) {
                    String language = photoIds.get(photoId);
                    Photo photoToArchive;
                    if (isArchived) {
                        photoToArchive = BaseDao.getArchivedDocumentById(photoId, Photo.class, language);
                    } else {
                        photoToArchive = BaseDao.getDocumentById(photoId, Photo.class, language);
                    }
                    if (photoToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            photoToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            photoToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(photoToArchive, language);

                        List<String> languagesToUpdate = photoToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Photo subPhotoToArchive = BaseDao.getDocumentByParentId(photoToArchive.getParentId(), Photo.class, languageToUpdate);
                                    if (subPhotoToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subPhotoToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subPhotoToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subPhotoToArchive, languageToUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
