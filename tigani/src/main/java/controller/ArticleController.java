package controller;

import com.github.slugify.Slugify;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;

import java.util.*;

import enums.StatusType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.*;

/**
 *
 * <AUTHOR>
 */
public class ArticleController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ArticleController.class.getName());

    public static TemplateViewRoute be_article_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_ARTICLE_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_article = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        attributes.put("requiredLanguage", language);
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("articleId"));
        if (oid != null) {
            Article loadedArticle = BaseDao.getDocumentById(oid, Article.class, language);
            attributes.put("article", loadedArticle);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Article loadedArticle = BaseDao.getDocumentByParentId(parentId, Article.class, language);
                if (loadedArticle != null) {
                    attributes.put("article", loadedArticle);
                }
            }
        }

        return Core.render(Pages.BE_ARTICLE, attributes, request);
    };

    public static Route be_article_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Article> articleListToCheck = new ArrayList<>();
        Map<String, Article> parentArticleMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Article> loadedArticles;
            // Create query options
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(new ArrayList<>(), 0, 0, "creation", "desc");
            if (loadArchived) {
                loadedArticles = BaseDao.getDocumentsByFilters(Article.class, queryOptions, languageToLoad, true);
            } else {
                loadedArticles = BaseDao.getDocumentsByFilters(Article.class, queryOptions, languageToLoad, false);
            }
            if (loadedArticles != null && !loadedArticles.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Article article : loadedArticles) {
                        parentArticleMap.put(article.getParentId(), article);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    articleListToCheck.addAll(loadedArticles);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!articleListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Article article : articleListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, article.getLanguage())) {
                            if (!parentArticleMap.containsKey(article.getParentId())) {
                                parentArticleMap.put(article.getParentId(), article);
                            }
                        }
                    }
                }
            }
        }
        List<Article> articleList = new ArrayList<>(parentArticleMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!articleList.isEmpty()) {
            for (Article article : articleList) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a language='").append(article.getLanguage()).append("' articleId='").append(article.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_ARTICLE + "?articleId=").append(article.getId()).append("&language=").append(article.getLanguage()).append("'>").append(article.getTitle()).append("</a>\",");
                json.append("\"").append(article.getCategory() != null ? article.getCategory() : "").append("\",");
                json.append("\"").append(article.getStatus() != null ? article.getStatus() : "").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(article.getPublication(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(StringUtils.join(article.getAvailableLanguages(), ", ")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(article.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(article.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_article_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("articleId"));
        Article newArticle;
        if (oid != null) {
            newArticle = BaseDao.getDocumentById(oid, Article.class, language);
            if (StringUtils.isBlank(newArticle.getCategoryIdentifier())) {
                Slugify slg = new Slugify();
                String categoryIdentifier = newArticle.getCategory();
                newArticle.setCategoryIdentifier(slg.slugify(categoryIdentifier));
            } 
            RequestUtils.mergeFromParams(params, newArticle);
        } else {
            newArticle = RequestUtils.createFromParams(params, Article.class);
            if (StringUtils.isBlank(newArticle.getIdentifier())) {
                newArticle.setIdentifier(newArticle.getTitle() + "-" + RoutesUtils.generateIdentifier());
            }
            if (StringUtils.isBlank(newArticle.getCategoryIdentifier())) {
                Slugify slg = new Slugify();
                String categoryIdentifier = newArticle.getCategory();
                newArticle.setCategoryIdentifier(slg.slugify(categoryIdentifier));
            }            
        }

        if (newArticle != null) {
            if (!params.containsKey("editorChoice")) {
                newArticle.setEditorChoice(false);
            }

            Article loadedByIdentifier = BaseDao.getDocumentByIdentifier(newArticle.getIdentifier(), Article.class, language);
            Article loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Article.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Article articleToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Article.class, languageToUpdate);
                        if (articleToUpdate != null) {
                            articleToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(articleToUpdate, articleToUpdate.getLanguage());
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage());
            }

            if (loadedByIdentifier != null && loadedByIdentifier.getId() != null) {
                if (newArticle.getId() == null || !loadedByIdentifier.getId().equals(newArticle.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Article with the same identifier already exists");
                }
            }

            newArticle.setTags(null);
            for (String param : params.keySet()) {
                // potrebbe essere items[0] se caricato dall'oggetto
                // oppure items_new[0] se arriva da input in pagina
                if (StringUtils.equals(param, "tags")) {
                    if (newArticle.getTags() == null) {
                        newArticle.setTags(new ArrayList<>());
                    }
                    List<String> splitted = Arrays.asList(StringUtils.split(params.get(param), "|"));
                    newArticle.setTags(splitted);
                }
            }
            newArticle.setLanguage(language);
            if (loadedByParentId != null) {
                newArticle.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newArticle.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newArticle.getParentId())) {
                newArticle.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newArticle.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newArticle, language);
                newArticle.setId(oid);

                BaseDao.insertLog(user, newArticle, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newArticle, language);
                BaseDao.insertLog(user, newArticle, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(articleFromQuery, newArticle);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newArticle, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newArticle, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newArticle != null ? newArticle.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_article_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String articles = params.get("articleIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(articles) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> articleIds = new HashMap<>();
            if (StringUtils.contains(articles, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(articles, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        articleIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(articles, "|"));
                if (parts.size() == 2) {
                    articleIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!articleIds.isEmpty()) {
                for (ObjectId articleId : articleIds.keySet()) {
                    String language = articleIds.get(articleId);
                    Article articleToArchive;
                    if (isArchived) {
                        articleToArchive = BaseDao.getArchivedDocumentById(articleId, Article.class, language);
                    } else {
                        articleToArchive = BaseDao.getDocumentById(articleId, Article.class, language);
                    }
                    if (articleToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            articleToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            articleToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(articleToArchive, language);

                        List<String> languagesToUpdate = articleToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Article subArticleToArchive = BaseDao.getDocumentByParentId(articleToArchive.getParentId(), Article.class, languageToUpdate);
                                    if (subArticleToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subArticleToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subArticleToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subArticleToArchive, languageToUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };

    public static TemplateViewRoute article_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        // Parse request parameters
        Map<String, String> params = new HashMap<>();
        Map<String, UploadedFile> files = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String language = RoutesUtils.language(request);
        String category = null;
        List<String> tags = null;
        if (request.params("category") != null) {
            category = request.params("category");
            attributes.put("categoryParam", category);
        }
        if (request.queryParams("tags") != null) {
            tags = Arrays.asList(StringUtils.split(request.queryParams("tags"), ","));
            attributes.put("tagsParam", tags);
        }

        // Build filters list
        List<Bson> filters = new ArrayList<>();

        // Always filter by confirmed status
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.PUBLISHED.name().toLowerCase()));
        filters.add(DaoFilters.getFilter("publication", DaoFiltersOperation.LTE, TimeUtils.today()));
        if (StringUtils.isNotBlank(category)) {
            filters.add(DaoFilters.getFilter("categoryIdentifier", DaoFiltersOperation.EQ, category));
        }
        if (tags != null && !tags.isEmpty()) {
            filters.add(DaoFilters.getFilter("tags", DaoFiltersOperation.IN, tags));
        }

        // Pagination parameters
        int page = 1;
        int limit = 10; // Default items per page

        try {
            if (StringUtils.isNotBlank(request.queryParams("page"))) {
                page = Integer.parseInt(request.queryParams("page"));
            }
            if (StringUtils.isNotBlank(request.queryParams("limit"))) {
                limit = Integer.parseInt(request.queryParams("limit"));
            }
        } catch (NumberFormatException ex) {
            LOGGER.warn("Invalid pagination parameters", ex);
        }

        int skip = (page - 1) * limit;

        // Sorting parameters
        String orderBy = StringUtils.defaultIfBlank(request.queryParams("orderBy"), "publication");
        String orderType = StringUtils.defaultIfBlank(request.queryParams("orderType"), "desc");

        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, skip, limit, orderBy, orderType);

        try {
            // Execute query
            List<Article> articles = BaseDao.getDocumentsByFilters(Article.class, queryOptions, language);
            long totalCount = BaseDao.countDocumentsByFilters(Article.class, queryOptions, language);

            // Add results to attributes
            attributes.put("articles", articles);
            attributes.put("totalCount", totalCount);
            attributes.put("currentPage", page);
            attributes.put("limit", limit);

        } catch (Exception ex) {
            LOGGER.error("Error executing article query", ex);
            attributes.put("articles", new ArrayList<Article>());
            attributes.put("totalCount", 0);
            attributes.put("error", "Unable to load results. Please try again.");
        }

        return Core.render(Pages.ARTICLE_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute article = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Article article = null;
        if (StringUtils.isNotBlank(identifier)) {
            article = BaseDao.getDocumentByIdentifier(identifier, Article.class, attributes.get("language").toString());
        }

        attributes.put("article", article);

        return Core.render(Pages.ARTICLE, attributes, request);
    };    
}
