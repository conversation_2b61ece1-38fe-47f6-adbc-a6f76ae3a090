package controller;

import static controller.LoginController.isPasswordCorrect;

import commons.BusinessCommons;
import commons.NotificationCommons;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import dao.UserDao;
import enums.LogType;
import enums.ProfileType;
import enums.StatusType;
import extensions.LabelsFunction;

import java.util.*;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

/**
 *
 * <AUTHOR>
 */
public class AccountController {
 
    private static final Logger LOGGER = LoggerFactory.getLogger(AccountController.class.getName());
    
    public static TemplateViewRoute account_login = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        boolean mailSent = BooleanUtils.toBoolean(request.queryParams("mailSent"));
        attributes.put("mailSent", mailSent);

        boolean mailSentRecover = BooleanUtils.toBoolean(request.queryParams("mailSentRecover"));
        attributes.put("mailSentRecover", mailSentRecover);

        return Core.render(Pages.ACCOUNT_LOGIN, attributes, request);
    };
    
    public static Route login_do = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        String language = RoutesUtils.language(request);
        String username = params.get("username");
        String password = params.get("password");
        User user = UserDao.loadUserByEmail(username);
        if (user != null) {
            // check password
            if (isPasswordCorrect(user, password)) {
                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Core.createSession(request, response, token);
                Core.addValueToSession(token, "user", user);
                return RoutesUtils.getLocalizedFullPath(request, "HOME", language);
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Password non corretta. Riprova.");
            }
        } else {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Email non registrata. Verifica l'indirizzo email o registrati.");
        }
    };
    
    public static TemplateViewRoute account_register = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_REGISTER, attributes, request);
    };
    
    public static TemplateViewRoute account_recover = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_RECOVER, attributes, request);
    };
    
    public static TemplateViewRoute account_verify = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_VERIFY, attributes, request);
    };
        
    public static Route recover_send = (Request request, Response response) -> {
        Map<String, UploadedFile> files = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);
        String language = RoutesUtils.language(request);

        if (params.containsKey("email")) {
            String email = params.get("email");
            User user = UserDao.loadUserByEmail(email);
            if (user != null) {
                String newPassword = PasswordHash.getCookieSafeSecureRandom(10);
                user.setPassword(PasswordHash.createHash(newPassword));
                BaseDao.updateDocument(user);
                BaseDao.insertLog(user, user, LogType.UPDATE);

                // mando mail conferma
                Map<String, Object> mailParams = new HashMap<>();
                mailParams.put("type", "ACCOUNT_PASSWORD_RESET");
                mailParams.put("language", language);
                mailParams.put("to", StringUtils.defaultIfBlank(user.getEmail(), ""));
                // Add user object for template access to user.keyword and user.registrationToken
                mailParams.put("user", user);
                mailParams.put("userPassword", newPassword);
                // Add baseUrl for template links
                String baseUrl = RoutesUtils.baseUrl(request);
                mailParams.put("baseUrl", baseUrl);
                NotificationCommons.sendTemplatedEmail(mailParams, false, false);

                response.redirect(RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_LOGIN", language) + "?mailSentRecover=true");
            }
        }
        
        return null;
    };  
    
    public static Route register_do = (Request request, Response response) -> {
        Map<String, UploadedFile> files = new HashMap<>();
        Map<String, String> params = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);
        String language = RoutesUtils.language(request);
        User newUser = RequestUtils.createFromParams(params, User.class);
        newUser.setPassword(PasswordHash.createHash(newUser.getPassword()));
        newUser.setUsername(newUser.getEmail());
        newUser.setProfileType(ProfileType.UNCONFIRMED.name().toLowerCase());
        // check if user already exists
        User existingUser = UserDao.loadUserByEmail(newUser.getEmail());
        if (existingUser != null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Questa email è già registrata. Prova ad accedere o usa un'altra email.");
        }
        Date now = new Date();
        newUser.setRegistrationSendDate(now);
        newUser.setRegistrationToken(UUID.randomUUID().toString());
        ObjectId insertedId = UserDao.insertDocument(newUser);
        if (insertedId != null) {
            String token = PasswordHash.getCookieSafeSecureRandom(32);
            Core.createSession(request, response, token);
            Core.addValueToSession(token, "user", newUser);
            // mando mail conferma
            Map<String, Object> mailParams = new HashMap<>();
            mailParams.put("type", "ACCOUNT_VERIFICATION");
            mailParams.put("language", language);
            mailParams.put("to", StringUtils.defaultIfBlank(newUser.getEmail(), ""));
            // Add user object for template access to user.keyword and user.registrationToken
            mailParams.put("user", newUser);
            // Add baseUrl for template links
            String baseUrl = RoutesUtils.baseUrl(request);
            mailParams.put("baseUrl", baseUrl);
            // Add userUrl for confirmation link - assuming a user confirmation endpoint
            String userUrl = baseUrl + RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_CONFIRM", language);
            mailParams.put("userUrl", userUrl);
            NotificationCommons.sendTemplatedEmail(mailParams, false, false);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_VERIFY", language);
        } else {
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_REGISTER", language);
        }
    };

    public static Route verify_send_mail = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);
        String language = RoutesUtils.language(request);

        if (user != null) {
            // mando mail conferma
            Map<String, Object> mailParams = new HashMap<>();
            mailParams.put("type", "ACCOUNT_VERIFICATION");
            mailParams.put("language", language);
            mailParams.put("to", StringUtils.defaultIfBlank(user.getEmail(), ""));
            // Add user object for template access to user.keyword and user.registrationToken
            mailParams.put("user", user);
            // Add baseUrl for template links
            String baseUrl = RoutesUtils.baseUrl(request);
            mailParams.put("baseUrl", baseUrl);
            // Add userUrl for confirmation link - assuming a user confirmation endpoint
            String userUrl = baseUrl + RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_CONFIRM", language);
            mailParams.put("userUrl", userUrl);
            NotificationCommons.sendTemplatedEmail(mailParams, false, false);

            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_LOGIN", language) + "?mailSent=true";
        } else {
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_REGISTER", language);
        }
    };

    public static TemplateViewRoute account_confirm = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);
        String language = RoutesUtils.language(request);

        String registrationToken = request.queryParams("registrationToken");
        if (StringUtils.isNotBlank(registrationToken)) {
            User user = UserDao.loadUserByRegistrationToken(registrationToken);
            if (user != null) {
                // check if registrationSendDate is less then 24h ago
                Date now = new Date();
                if (now.getTime() - user.getRegistrationSendDate().getTime() > ********) {
                    // TODO: FARE PAGINA PER DIRE CHE LA RICHIESTA E' SCADUTA
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Il link di conferma è scaduto. Registrati nuovamente per ricevere una nuova email di attivazione.");
                }

                user.setProfileType(ProfileType.CUSTOMER.name().toLowerCase());
                user.setRegistrationDate(now);
                BaseDao.updateDocument(user);

                // mando mail conferma registrazione
                Map<String, Object> mailParams = new HashMap<>();
                mailParams.put("type", "ACCOUNT_WELCOME");
                mailParams.put("language", language);
                mailParams.put("to", StringUtils.defaultIfBlank(user.getEmail(), ""));
                // Add user object for template access to user.keyword and user.registrationToken
                mailParams.put("user", user);
                // Add baseUrl for template links
                String baseUrl = RoutesUtils.baseUrl(request);
                mailParams.put("baseUrl", baseUrl);
                NotificationCommons.sendTemplatedEmail(mailParams, false, false);

                String token = PasswordHash.getCookieSafeSecureRandom(32);
                Core.createSession(request, response, token);
                Core.addValueToSession(token, "user", user);

                response.redirect(RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_WELCOME", language));
                return null;
            }
        }

        response.redirect(RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_LOGIN", language));
        return null;
    };

    public static TemplateViewRoute account_welcome = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        return Core.render(Pages.ACCOUNT_WELCOME, attributes, request);
    };
    
    public static TemplateViewRoute account_logout = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            Core.destroySession(request, response);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        return Core.render(Pages.ACCOUNT_LOGIN, attributes, request);
    };

    public static TemplateViewRoute account_info = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);
        return Core.render(Pages.ACCOUNT_INFO, attributes, request);
    };

    public static Route account_info_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            // gestione cambio email
            if (params.containsKey("email")) {
                if (!StringUtils.equalsIgnoreCase(user.getEmail(), params.get("email"))) {
                    User tmpUser = UserDao.loadUserByEmail(params.get("email"));
                    if (tmpUser != null) {
                        throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Questa email è già utilizzata da un altro utente. Scegli un indirizzo email diverso.");
                    }
                }
            }

            user = RequestUtils.mergeFromParams(params, user);
            // update user on redis
            String token = Core.getSessionToken(request);
            Core.addValueToSession(token, "user", user);
            BaseDao.updateDocument(user);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_INFO", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_password_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            String currentPassword = params.get("currentPassword");
            String newPassword = params.get("newPassword");
            String newPasswordConfirmation = params.get("newPasswordConfirmation");

            if (!isPasswordCorrect(user, currentPassword)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Password attuale non corretta. Verifica e riprova.");
            }
            if (!newPassword.equals(newPasswordConfirmation)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Le nuove password non coincidono. Assicurati di aver digitato la stessa password in entrambi i campi.");
            }

            user.setPassword(PasswordHash.createHash(newPassword));
            // update user on redis
            String token = Core.getSessionToken(request);
            Core.addValueToSession(token, "user", user);
            BaseDao.updateDocument(user);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_INFO", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_delete = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            String confirmDeletion = params.get("confirmDeletion");
            if (confirmDeletion == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.confirm.deletion"));
            }
            if (!confirmDeletion.equals("on")) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.confirm.deletion"));
            }

            user.setCancelled(true);
            BaseDao.updateDocument(user);
            Core.destroySession(request, response);
            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_REGISTER", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_image_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new HashMap<>();
            RequestUtils.parseRequest(request, params, files);

            BaseDao.deleteImage(user, "imageId");
            if (!files.isEmpty()) {
                BaseDao.saveImage(files.entrySet().iterator().next().getValue(), user, "imageId", true);
            }
            String token = Core.getSessionToken(request);
            Core.addValueToSession(token, "user", user);
            BaseDao.updateDocument(user);

            return RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_INFO", language);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static TemplateViewRoute account_favourites = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        List<Bson> filters = new ArrayList<>();
        filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
        List<BusinessPreferred> businessPreferreds = BaseDao.getDocumentsByFilters(BusinessPreferred.class, queryOptions, false);
        // Load them
        List<Business> businesses = new ArrayList<>();
        for (BusinessPreferred businessPreferred : businessPreferreds) {
            businesses.add(BaseDao.getDocumentById(businessPreferred.getBusinessId(), Business.class));
        }
        attributes.put("businesses", businesses);

        return Core.render(Pages.ACCOUNT_FAVOURITES, attributes, request);
    };

    public static TemplateViewRoute account_businesses = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes);

        // create query options for userId and get the business list
        List<Bson> filters = new ArrayList<>();
        if (user != null) {
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
        }
        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, "creation", null);
        List<Business> businesses = BaseDao.getDocumentsByFilters(Business.class, queryOptions, true);
        List<BusinessEntry> businessEntries = BusinessCommons.toEntries(businesses);

        // create 3 lists: status confirmed, status unconfirmed and archived true
        List<BusinessEntry> confirmedBusinesses = new ArrayList<>();
        List<BusinessEntry> unconfirmedBusinesses = new ArrayList<>();
        List<BusinessEntry> archivedBusinesses = new ArrayList<>();
        for (BusinessEntry entry : businessEntries) {
            if (BooleanUtils.isTrue(entry.getBusiness().getArchived())) {
                archivedBusinesses.add(entry);
            } else if (StringUtils.equalsIgnoreCase(entry.getBusiness().getStatus(), StatusType.CONFIRMED.name())) {
                confirmedBusinesses.add(entry);
            } else if (StringUtils.equalsIgnoreCase(entry.getBusiness().getStatus(), StatusType.UNCONFIRMED.name())) {
                unconfirmedBusinesses.add(entry);
            }
        }
        attributes.put("confirmedBusinesses", confirmedBusinesses);
        attributes.put("unconfirmedBusinesses", unconfirmedBusinesses);
        attributes.put("archivedBusinesses", archivedBusinesses);
        attributes.put("businesses", businesses);

        return Core.render(Pages.ACCOUNT_BUSINESSES, attributes, request);
    };

    public static TemplateViewRoute account_business_info = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("entry", BusinessCommons.toEntry(business));
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }        
        
        return Core.render(Pages.ACCOUNT_BUSINESS_INFO, attributes, request);
    };

    public static TemplateViewRoute account_business_services = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("entry", BusinessCommons.toEntry(business));
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }      
        
        return Core.render(Pages.ACCOUNT_BUSINESS_SERVICES, attributes, request);
    };

    public static TemplateViewRoute account_business_bookings = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("entry", BusinessCommons.toEntry(business));
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }      
        
        return Core.render(Pages.ACCOUNT_BUSINESS_BOOKINGS, attributes, request);
    };

    public static TemplateViewRoute account_business_reviews = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("entry", BusinessCommons.toEntry(business));
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }      
        
        return Core.render(Pages.ACCOUNT_BUSINESS_REVIEWS, attributes, request);
    };
    
    public static TemplateViewRoute account_business_settings = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("entry", BusinessCommons.toEntry(business));
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }      
        
        return Core.render(Pages.ACCOUNT_BUSINESS_SETTINGS, attributes, request);
    };
    
    public static TemplateViewRoute account_business_revision = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);
        
        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("business", business);
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }
        
        return Core.render(Pages.ACCOUNT_BUSINESS_REVISION, attributes, request);
    };
    
    public static TemplateViewRoute account_business_messages = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);
        
        String language = RoutesUtils.language(request);
        
        Business business;

        try {
            ObjectId oid = RequestUtils.toObjectId(request.queryParams("oid"));
            business = BaseDao.getDocumentById(oid, Business.class);

            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
            }
            
            attributes.put("entry", BusinessCommons.toEntry(business));
            
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("businessId", DaoFiltersOperation.EQ, oid));
            filters.add(DaoFilters.getFilter("read", DaoFiltersOperation.NE, true));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            long newCount = BaseDao.countDocumentsByFilters(Contact.class, queryOptions);

            attributes.put("newCount", newCount);
            
            filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("businessId", DaoFiltersOperation.EQ, oid));
            queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<Contact> contacts = BaseDao.getDocumentsByFilters(Contact.class, queryOptions);

            attributes.put("contacts", contacts);
            
        } catch (IllegalArgumentException ex) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
        }

        
        return Core.render(Pages.ACCOUNT_BUSINESS_MESSAGES, attributes, request);
    };

    public static Route account_business_message_update = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.CUSTOMER);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String contactIdParam = params.get("contactId");
        if (StringUtils.isBlank(contactIdParam)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "contact ID is required");
        }

        ObjectId contactId = RequestUtils.toObjectId(contactIdParam);
        ObjectId userId = user.getId();

        if (contactId == null || userId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Invalid user or contact ID");
        }

        try {
            // Create QueryOptions to find existing BusinessPreferred record
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("_id", DaoFiltersOperation.EQ, contactId));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);

            // Check if BusinessPreferred record exists
            Contact contact = BaseDao.getDocumentByFilters(Contact.class, queryOptions);

            if (contact != null) {
                // Record exists, delete it (remove from preferred)
                contact.setRead(!BooleanUtils.isTrue(contact.getRead()));
                BaseDao.updateDocument(contact);
                BaseDao.insertLog(user, contact, LogType.UPDATE);

                if (contact.getBusinessId() != null) {
                    Business business = BaseDao.getDocumentById(contact.getBusinessId(), Business.class);
                    BusinessUtils.updateBusinessAnalytics(business, "messagesNotRead");
                }
                
                return "updated";
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Invalid contact");
            }
        } catch (Exception ex) {
            LOGGER.error("Error managing business preferred for userId: " + userId + ", businessId: " + contactId, ex);
            throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, "Internal server error");
        }
    };    
    
}
