package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.ProfileType;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Slider;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.TemplateViewRoute;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class SliderController {

    private static final Logger LOGGER = LoggerFactory.getLogger(SliderController.class.getName());

    public static TemplateViewRoute slider = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        List<Slider> sliderList = BaseDao.getDocuments(Slider.class, null);
        attributes.put("sliderList", sliderList);

        return Core.render(Pages.BE_SLIDER, attributes, request);
    };

    public static TemplateViewRoute slider_edit = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("sliderId"));
        if (oid != null) {
            Slider loadedSlider = BaseDao.getDocumentById(oid, Slider.class, null);
            attributes.put("slider", loadedSlider);
        }

        return Core.render(Pages.BE_SLIDER_EDIT, attributes, request);
    };

    public static Route slider_edit_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        ObjectId oid = RequestUtils.toObjectId(request.queryParams("sliderId"));
        Slider newSlider;
        if (oid != null) {
            newSlider = BaseDao.getDocumentById(oid, Slider.class, null);
            RequestUtils.mergeFromParams(params, newSlider);
        } else {
            newSlider = RequestUtils.createFromParams(params, Slider.class);
        }

        if (newSlider != null) {
            if (oid == null) {
                BaseDao.insertDocument(newSlider, null);
            } else {
                BaseDao.updateDocument(newSlider, null);
            }
        }

        response.redirect(RoutesUtils.contextPath(request) + Routes.BE_SLIDER);
        return null;
    };
}
