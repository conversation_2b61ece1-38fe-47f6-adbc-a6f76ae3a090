package controller;

import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.LogType;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Script;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.Defaults;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class ScriptController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ScriptController.class.getName());

    public static TemplateViewRoute be_script_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_SCRIPT_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_script = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
            attributes.put("requiredLanguage", language);
        }
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("scriptId"));
        if (oid != null) {
            Script loadedScript = BaseDao.getDocumentById(oid, Script.class, language);
            attributes.put("script", loadedScript);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Script loadedScript = BaseDao.getDocumentByParentId(parentId, Script.class, language);
                if (loadedScript != null) {
                    attributes.put("script", loadedScript);
                }
            }
        }

        return Core.render(Pages.BE_SCRIPT, attributes, request);
    };

    public static Route be_script_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Script> scriptListToCheck = new ArrayList<>();
        Map<String, Script> parentScriptMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Script> loadedScripts;
            if (loadArchived) {
                loadedScripts = BaseDao.getArchivedDocuments(Script.class, languageToLoad);
            } else {
                loadedScripts = BaseDao.getDocuments(Script.class, languageToLoad);
            }
            if (loadedScripts != null && !loadedScripts.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Script script : loadedScripts) {
                        parentScriptMap.put(script.getParentId(), script);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    scriptListToCheck.addAll(loadedScripts);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!scriptListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Script script : scriptListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, script.getLanguage())) {
                            if (!parentScriptMap.containsKey(script.getParentId())) {
                                parentScriptMap.put(script.getParentId(), script);
                            }
                        }
                    }
                }
            }
        }
        List<Script> scriptList = new ArrayList<>(parentScriptMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!scriptList.isEmpty()) {
            for (Script script : scriptList) {
                json.append("[");
                json.append("\"<a language='").append(script.getLanguage()).append("' scriptId='").append(script.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_SCRIPT + "?scriptId=").append(script.getId()).append("&language=").append(script.getLanguage()).append("'>").append(script.getKey()).append("</a>\",");
                json.append("\"").append(StringUtils.join(script.getAvailableLanguages(), ", ")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(script.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_script_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("scriptId"));
        Script newScript;
        if (oid != null) {
            newScript = BaseDao.getDocumentById(oid, Script.class, language);
            RequestUtils.mergeFromParams(params, newScript);
        } else {
            newScript = RequestUtils.createFromParams(params, Script.class);
        }

        if (newScript != null) {
            Script loadedByKey = BaseDao.getDocumentByKey(newScript.getKey(), Script.class, language);
            Script loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Script.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Script scriptToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Script.class, languageToUpdate);
                        if (scriptToUpdate != null) {
                            scriptToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(scriptToUpdate, scriptToUpdate.getLanguage());
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage());
            }

            if (loadedByKey != null && loadedByKey.getId() != null) {
                if (newScript.getId() == null || !loadedByKey.getId().equals(newScript.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Script with the same key already exists");
                }
            }

            newScript.setLanguage(language);
            if (loadedByParentId != null) {
                newScript.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newScript.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newScript.getParentId())) {
                newScript.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newScript.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newScript, language);
                newScript.setId(oid);

                BaseDao.insertLog(user, newScript, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newScript, language);
                BaseDao.insertLog(user, newScript, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(scriptFromQuery, newScript);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newScript, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newScript, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newScript != null ? newScript.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_script_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String scripts = params.get("scriptIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(scripts) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> scriptIds = new HashMap<>();
            if (StringUtils.contains(scripts, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(scripts, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        scriptIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(scripts, "|"));
                if (parts.size() == 2) {
                    scriptIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!scriptIds.isEmpty()) {
                for (ObjectId scriptId : scriptIds.keySet()) {
                    String language = scriptIds.get(scriptId);
                    Script scriptToArchive;
                    if (isArchived) {
                        scriptToArchive = BaseDao.getArchivedDocumentById(scriptId, Script.class, language);
                    } else {
                        scriptToArchive = BaseDao.getDocumentById(scriptId, Script.class, language);
                    }
                    if (scriptToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            scriptToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            scriptToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(scriptToArchive, language);

                        List<String> languagesToUpdate = scriptToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Script subScriptToArchive = BaseDao.getDocumentByParentId(scriptToArchive.getParentId(), Script.class, languageToUpdate);
                                    if (subScriptToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subScriptToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subScriptToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subScriptToArchive, languageToUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
