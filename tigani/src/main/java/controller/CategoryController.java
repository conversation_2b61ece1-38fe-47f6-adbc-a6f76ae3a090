package controller;

import com.github.slugify.Slugify;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import enums.LogType;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Category;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.DateTimeUtils;
import utils.Defaults;
import utils.RequestUtils;
import utils.RoutesUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class CategoryController {

    private static final Logger LOGGER = LoggerFactory.getLogger(CategoryController.class.getName());

    public static TemplateViewRoute be_category_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_CATEGORY_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_category = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
            attributes.put("requiredLanguage", language);
        }
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("categoryId"));
        if (oid != null) {
            Category loadedCategory = BaseDao.getDocumentById(oid, Category.class, language);
            attributes.put("category", loadedCategory);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Category loadedCategory = BaseDao.getDocumentByParentId(parentId, Category.class, language);
                if (loadedCategory != null) {
                    attributes.put("category", loadedCategory);
                }
            }
        }

        return Core.render(Pages.BE_CATEGORY, attributes, request);
    };

    public static Route be_category_data = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Category> categoryListToCheck = new ArrayList<>();
        Map<String, Category> parentCategoryMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Category> loadedCategorys;
            if (loadArchived) {
                loadedCategorys = BaseDao.getArchivedDocuments(Category.class, languageToLoad);
            } else {
                loadedCategorys = BaseDao.getDocuments(Category.class, languageToLoad);
            }
            if (loadedCategorys != null && !loadedCategorys.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Category category : loadedCategorys) {
                        parentCategoryMap.put(category.getParentId(), category);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    categoryListToCheck.addAll(loadedCategorys);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!categoryListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Category category : categoryListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, category.getLanguage())) {
                            if (!parentCategoryMap.containsKey(category.getParentId())) {
                                parentCategoryMap.put(category.getParentId(), category);
                            }
                        }
                    }
                }
            }
        }
        List<Category> categoryList = new ArrayList<>(parentCategoryMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!categoryList.isEmpty()) {
            for (Category category : categoryList) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a language='").append(category.getLanguage()).append("' categoryId='").append(category.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_CATEGORY + "?categoryId=").append(category.getId()).append("&language=").append(category.getLanguage()).append("'>").append(category.getTitle()).append("</a>\",");
                json.append("\"").append(StringUtils.join(category.getAvailableLanguages(), ", ")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(category.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_category_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        if (language == null) {
            language = Defaults.DEFAULT_USER_LANGUAGE;
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("categoryId"));
        Category newCategory;
        if (oid != null) {
            newCategory = BaseDao.getDocumentById(oid, Category.class, language);
            RequestUtils.mergeFromParams(params, newCategory);
        } else {
            newCategory = RequestUtils.createFromParams(params, Category.class);
            if (StringUtils.isBlank(newCategory.getIdentifier())) {
                newCategory.setIdentifier(newCategory.getTitle() + "-" + RoutesUtils.generateIdentifier());
            }
        }

        if (newCategory != null) {
            Category loadedByIdentifier = BaseDao.getDocumentByIdentifier(newCategory.getIdentifier(), Category.class, language);
            Category loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Category.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Category categoryToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Category.class, languageToUpdate);
                        if (categoryToUpdate != null) {
                            categoryToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(categoryToUpdate, categoryToUpdate.getLanguage());
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage());
            }

            if (loadedByIdentifier != null && loadedByIdentifier.getId() != null) {
                if (newCategory.getId() == null || !loadedByIdentifier.getId().equals(newCategory.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Category with the same identifier already exists");
                }
            }

            newCategory.setLanguage(language);
            if (loadedByParentId != null) {
                newCategory.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newCategory.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newCategory.getParentId())) {
                newCategory.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newCategory.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newCategory, language);
                newCategory.setId(oid);

                BaseDao.insertLog(user, newCategory, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newCategory, language);
                BaseDao.insertLog(user, newCategory, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(categoryFromQuery, newCategory);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newCategory, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newCategory, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newCategory != null ? newCategory.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_category_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String categorys = params.get("categoryIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(categorys) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> categoryIds = new HashMap<>();
            if (StringUtils.contains(categorys, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(categorys, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        categoryIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(categorys, "|"));
                if (parts.size() == 2) {
                    categoryIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!categoryIds.isEmpty()) {
                for (ObjectId categoryId : categoryIds.keySet()) {
                    String language = categoryIds.get(categoryId);
                    Category categoryToArchive;
                    if (isArchived) {
                        categoryToArchive = BaseDao.getArchivedDocumentById(categoryId, Category.class, language);
                    } else {
                        categoryToArchive = BaseDao.getDocumentById(categoryId, Category.class, language);
                    }
                    if (categoryToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            categoryToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            categoryToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(categoryToArchive, language);

                        List<String> languagesToUpdate = categoryToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Category subCategoryToArchive = BaseDao.getDocumentByParentId(categoryToArchive.getParentId(), Category.class, languageToUpdate);
                                    if (subCategoryToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subCategoryToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subCategoryToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subCategoryToArchive, languageToUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };
}
