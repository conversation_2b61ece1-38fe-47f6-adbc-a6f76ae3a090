package controller;

import commons.NotificationCommons;
import core.Core;
import core.Pages;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import enums.StatusType;
import extensions.LabelsFunction;
import java.util.*;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import static com.mongodb.client.model.Filters.and;
import static com.mongodb.client.model.Filters.eq;
import core.Routes;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import spark.*;
import utils.*;

/**
 *
 * <AUTHOR>
 */
public class BusinessController {

    private static final Logger LOGGER = LoggerFactory.getLogger(BusinessController.class.getName());

    public static TemplateViewRoute be_business_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_BUSINESS_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_business = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("businessId"));
        if (oid != null) {
            Business loadedBusiness = BaseDao.getDocumentById(oid, Business.class);
            attributes.put("business", loadedBusiness);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Business loadedBusiness = BaseDao.getDocumentByParentId(parentId, Business.class);
                if (loadedBusiness != null) {
                    attributes.put("business", loadedBusiness);
                }
            }
        }

        return Core.render(Pages.BE_BUSINESS, attributes, request);
    };

    public static Route be_business_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Business> loadedBusinesses;
        if (loadArchived) {
            loadedBusinesses = BaseDao.getArchivedDocuments(Business.class);
        } else {
            loadedBusinesses = BaseDao.getDocuments(Business.class);
        }
        

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!loadedBusinesses.isEmpty()) {
            for (Business business : loadedBusinesses) {
                
                User businessOwner = null;
                if (business.getUserId() != null) { // assumendo che business abbia getUserId()
                    businessOwner = BaseDao.getDocumentById(business.getUserId(), User.class);
                }
                
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a target='_blank' businessId='").append(business.getId()).append("' href='").append(RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", null)).append("?oid=").append(business.getId()).append("&step=5'>")
                .append(business.getFullname() != null && !business.getFullname().trim().isEmpty() 
                    ? business.getFullname() 
                    : "N.D.")
                .append("</a>\",");
                if (businessOwner != null) {
                    json.append("\"<a target='_blank' userId='").append(businessOwner.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_USER).append("?userId=").append(businessOwner.getId()).append("'>").append((StringUtils.defaultIfBlank(businessOwner.getName(), "N.D."))).append(" ").append((StringUtils.defaultIfBlank(businessOwner.getLastname(), ""))).append("</a>\",");
                } else {
                    json.append("\"N.D.\",");
                }
                List<String> categoryList = new ArrayList<>();
                if (BooleanUtils.isTrue(business.getDiving())) {
                    categoryList.add("Diving");
                }
                if (BooleanUtils.isTrue(business.getFreedivingApnea())) {
                    categoryList.add("Freediving Apnea");
                }
                if (BooleanUtils.isTrue(business.getExperience())) {
                    categoryList.add("Experience");
                }
                if (BooleanUtils.isTrue(business.getSport())) {
                    categoryList.add("Sport");
                }
                json.append("\"").append(categoryList != null && !categoryList.isEmpty() 
                ? StringUtils.join(categoryList, ", ") 
                : "Nessuna").append("\",");
                json.append("\"").append(business.getStatus() != null ? business.getStatus() : "").append("\",");
                json.append("\"").append(BooleanUtils.isTrue(business.getEditorChoice()) ? "Sì" : "No").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(business.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(business.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static TemplateViewRoute account_business_edit = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        // TODO: CAMBIARE DA UNCONFIRMED A STANDARD (CONFERMATO TRAMITE MAIL)
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Business business = null;
            ObjectId businessId = null;

            // Check if there's an "oid" parameter in the URL
            String oidParam = request.queryParams("oid");
            if (oidParam != null && !oidParam.trim().isEmpty() && !"-".equals(oidParam)) {
                try {
                    businessId = new ObjectId(oidParam);
                    business = BaseDao.getDocumentById(businessId, Business.class);

                    if (business == null) {
                        // Business not found, redirect to create new one
                        String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                        response.redirect(redirectUrl);
                        return null;
                    } else {
                        // check if the user can view/edit it
                        ProfileType minRole = ProfileType.OPERATOR;
                        int userRoleValue = ProfileType.getValueForRole(user.getProfileType());

                        if (business.getUserId() != null && !business.getUserId().equals(user.getId()) && userRoleValue < minRole.getValue()) {
                            String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "HOME", language);
                            response.redirect(redirectUrl);
                            return null;
                        }
                    }
                } catch (IllegalArgumentException ex) {
                    // Invalid ObjectId, redirect to create new one
                    String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                    response.redirect(redirectUrl);
                    return null;
                }
            } else {
                // Check if there's any Business created by the user with status "draft"
                List<Bson> filters = new ArrayList<>();
                filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
                filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, "draft"));
                QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);
                business = BaseDao.getDocumentByFilters(Business.class, queryOptions);

                if (business != null) {
                    // Draft business found - ask user what to do
                    attributes.put("draftBusiness", business);
                    attributes.put("showDraftChoice", true);

                    // Don't redirect, show the draft choice page
                } else {
                    // Create new Business
                    business = new Business();
                    business.setStatus(StatusType.DRAFT.name().toLowerCase());
                    business.setUserId(user.getId());

                    try {
                        businessId = BaseDao.insertDocument(business);
                        business.setId(businessId);

                        // Redirect to the same page with the new business ID
                        String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                        redirectUrl += "?oid=" + businessId.toString();
                        response.redirect(redirectUrl);
                        return null;
                    } catch (Exception ex) {
                        LOGGER.error("Error creating new business", ex);
                        throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.create.business"));
                    }
                }
            }

            // Add business to attributes for the template
            attributes.put("business", business);

            // manage gallery param
            attributes.put("gallery", StringUtils.isNotBlank(request.queryParams("gallery")));

            // step param
            attributes.put("step", StringUtils.isNotBlank(request.queryParams("step")) ? Integer.parseInt(request.queryParams("step")) : 1);
        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }

        return Core.render(Pages.ACCOUNT_BUSINESS_EDIT, attributes, request);
    };

    public static Route account_business_edit_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, UploadedFile> files = new HashMap<>();
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, files);

            // The oid parameter must be present in the form submission
            String oidParam = params.get("oid");
            if (oidParam == null || oidParam.trim().isEmpty() || "-".equals(oidParam)) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.business.id.required"));
            }

            ObjectId businessId;
            Business business;

            try {
                businessId = new ObjectId(oidParam);
                business = BaseDao.getDocumentById(businessId, Business.class);

                if (business == null) {
                    throw Spark.halt(HttpStatus.NOT_FOUND_404, LabelsFunction.description(language, "error.business.not.found"));
                }
            } catch (IllegalArgumentException ex) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.business.id"));
            }

            // Merge form parameters into the business object
            business = RequestUtils.mergeFromParams(params, business);

            // Generate unique identifier if needed
            try {
                BusinessUtils.generateUniqueIdentifier(business);
            } catch (Exception ex) {
                LOGGER.error("Error generating unique identifier for business", ex);
                throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.generate.identifier"));
            }

            if (StringUtils.isBlank(business.getLat()) || StringUtils.isBlank(business.getLng())) {
                Location location = geocode(business);
                if (location != null) {
                    business.setLat(location.getCoordinates()[0].toString());
                    business.setLng(location.getCoordinates()[1].toString());
                }
            }

            try {
                // Update the business in the database
                BaseDao.updateDocument(business);

                boolean imageDeleted = false;
                if (request.queryParams().contains("step") && request.queryParams().contains("gallery")) {
                    Integer step = Integer.parseInt(request.queryParams("step"));
                    if (step == 3) {
                        boolean gallery = BooleanUtils.toBoolean(request.queryParams("gallery"));
                        if (gallery) {
                            BaseDao.deleteImages(business, "imageIds");
                            BaseDao.deleteImages(business, "logoId");
                            imageDeleted = true;
                        }
                    }
                }
                if (!files.isEmpty()) {
                    // Split files into logo and other images
                    List<UploadedFile> logoFiles = new ArrayList<>();
                    List<UploadedFile> otherFiles = new ArrayList<>();

                    for (Map.Entry<String, UploadedFile> entry : files.entrySet()) {
                        if (StringUtils.equals("logoId", entry.getKey())) {
                            logoFiles.add(entry.getValue());
                        } else {
                            otherFiles.add(entry.getValue());
                        }
                    }

                    // Handle logo files
                    if (!logoFiles.isEmpty()) {
                        if (!imageDeleted) {
                            BaseDao.deleteImages(business, "logoId");
                        }
                        BaseDao.saveImages(logoFiles, business, "logoId");
                    }

                    // Handle other image files
                    if (!otherFiles.isEmpty()) {
                        if (!imageDeleted) {
                            BaseDao.deleteImages(business, "imageIds");
                        }
                        BaseDao.saveImages(otherFiles, business, "imageIds");
                    }
                }

                // Redirect to the business-new page with the business ID
                /*String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);
                redirectUrl += "?oid=" + businessId.toString();*/
                String redirectUrl = "ok";

                if (params.containsKey("status")) {
                    if (StringUtils.isNotBlank(business.getStatus())) {
                        if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.UNCONFIRMED.name().toLowerCase())) {
                            redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_REVISION", language) + "?oid=" + businessId;
                            

                            // mando mail conferma
                            Map<String, Object> mailParams = new HashMap<>();
                            mailParams.put("type", "ACCOUNT_ACTIVITY_REVISION");
                            mailParams.put("language", language);
                            mailParams.put("to", StringUtils.defaultIfBlank(user.getEmail(), ""));
                            // Add baseUrl for template links
                            String baseUrl = RoutesUtils.baseUrl(request);
                            mailParams.put("baseUrl", baseUrl);
                            mailParams.put("activity", business);
                            mailParams.put("activityName", StringUtils.defaultIfBlank(business.getFullname(), "N.D."));
                            List<String> types = new ArrayList<>();
                            if (BooleanUtils.isTrue(business.getDiving())) {
                                types.add("Diving");
                            }
                            if (BooleanUtils.isTrue(business.getFreedivingApnea())) {
                                types.add("Freediving Apnea");
                            }
                            if (BooleanUtils.isTrue(business.getExperience())) {
                                types.add("Experience");
                            }
                            if (BooleanUtils.isTrue(business.getSport())) {
                                types.add("Sport");
                            }
                            mailParams.put("activityType", StringUtils.join(types, ", "));
                            mailParams.put("activityLocation", StringUtils.defaultIfBlank(business.getFulladdress(), "N.D."));
                            mailParams.put("activitySendDate", DateTimeUtils.dateToString(new Date(), "dd/MM/YYYY"));
                            NotificationCommons.sendTemplatedEmail(mailParams, false, false);

                            // mando mail conferma
                            Company cmp = BaseDao.getDocumentByClass(Company.class);
                            mailParams.put("type", "ACCOUNT_ACTIVITY_TO_REVISION");
                            mailParams.put("to", StringUtils.defaultIfBlank(cmp.getEmail(), ""));
                            mailParams.put("userName", user.getName());
                            mailParams.put("userEmail", user.getEmail());
                            NotificationCommons.sendTemplatedEmail(mailParams, false, false);
                        }
                        if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.CONFIRMED.name().toLowerCase())) {
                            // mando mail conferma
                            User businessOwner = BaseDao.getDocumentById(business.getUserId(), User.class);
                            if (businessOwner != null && StringUtils.isNotBlank(businessOwner.getEmail())) {
                                Map<String, Object> mailParams = new HashMap<>();
                                mailParams.put("type", "ACCOUNT_ACTIVITY_ACCEPTED");
                                mailParams.put("language", language);
                                mailParams.put("to", StringUtils.defaultIfBlank(businessOwner.getEmail(), ""));
                                // Add user object for template access to user.keyword and user.registrationToken
                                mailParams.put("user", user);
                                mailParams.put("business", business);
                                // Add baseUrl for template links
                                String baseUrl = RoutesUtils.baseUrl(request);
                                mailParams.put("baseUrl", baseUrl);
                                NotificationCommons.sendTemplatedEmail(mailParams, false, false);
                            }
                        }
                    }
                }

                return redirectUrl;
            } catch (Exception ex) {
                LOGGER.error("Error updating business", ex);
                throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.update.business"));
            }

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route account_business_draft_choice = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes, ProfileType.UNCONFIRMED);

        if (user != null) {
            String language = RoutesUtils.language(request);
            Map<String, String> params = new LinkedHashMap<>();
            RequestUtils.parseRequest(request, params, null);

            String choice = params.get("choice");
            String draftId = params.get("draftId");

            if (choice == null || draftId == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.request"));
            }

            String redirectUrl = RoutesUtils.getLocalizedFullPath(request, "ACCOUNT_BUSINESS_EDIT", language);

            if (StringUtils.equals("use_draft", choice)) {
                // User wants to use the existing draft
                redirectUrl += "?oid=" + draftId;
            } else if (StringUtils.equals("start_fresh", choice)) {
                // User wants to start fresh - delete the draft and create new
                try {
                    ObjectId draftObjectId = new ObjectId(draftId);
                    Business draftBusiness = BaseDao.getDocumentById(draftObjectId, Business.class);

                    if (draftBusiness != null) {
                        // Delete the draft business
                        BaseDao.deleteDocument(draftBusiness);
                    }
                } catch (Exception ex) {
                    LOGGER.error("Error handling draft choice", ex);
                    throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, LabelsFunction.description(language, "error.unable.to.process.choice"));
                }
            } else {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, LabelsFunction.description(language, "error.invalid.choice"));
            }

            response.redirect(redirectUrl);
            return null;

        } else {
            throw Spark.halt(HttpStatus.UNAUTHORIZED_401);
        }
    };

    public static Route be_business_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        String language = RoutesUtils.language(request);

        String operation = params.get("operation");
        String businessIds = params.get("businessIds");

        if (StringUtils.isNotBlank(operation) && StringUtils.isNotBlank(businessIds)) {
            String[] ids = businessIds.split(",");
            for (String id : ids) {
                ObjectId oid = RequestUtils.toObjectId(id);
                if (oid != null) {
                    Business business = BaseDao.getDocumentById(oid, Business.class);
                    if (business != null) {
                        switch (operation) {
                            case "delete":
                                BaseDao.deleteDocument(business);
                                BaseDao.insertLog(user, business, LogType.DELETE);
                                break;
                            case "archive":
                                business.setArchived(true);
                                BaseDao.updateDocument(business);
                                BaseDao.insertLog(user, business, LogType.UPDATE);
                                break;
                            case "unarchive":
                                business.setArchived(false);
                                BaseDao.updateDocument(business);
                                BaseDao.insertLog(user, business, LogType.UPDATE);
                                break;
                            case "confirm":
                                if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.UNCONFIRMED.name().toLowerCase())) {
                                    business.setStatus(StatusType.CONFIRMED.name().toLowerCase());
                                    BaseDao.updateDocument(business);
                                    BaseDao.insertLog(user, business, LogType.UPDATE);

                                    // mando mail conferma
                                    User businessOwner = BaseDao.getDocumentById(business.getUserId(), User.class);
                                    if (businessOwner != null && StringUtils.isNotBlank(businessOwner.getEmail())) {
                                        Map<String, Object> mailParams = new HashMap<>();
                                        mailParams.put("type", "ACCOUNT_ACTIVITY_ACCEPTED");
                                        mailParams.put("language", language);
                                        mailParams.put("to", StringUtils.defaultIfBlank(businessOwner.getEmail(), ""));
                                        // Add user object for template access to user.keyword and user.registrationToken
                                        mailParams.put("user", user);
                                        mailParams.put("business", business);
                                        // Add baseUrl for template links
                                        String baseUrl = RoutesUtils.baseUrl(request);
                                        mailParams.put("baseUrl", baseUrl);
                                        NotificationCommons.sendTemplatedEmail(mailParams, false, false);
                                    }
                                    break;
                                }
                            case "reject":
                                if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.UNCONFIRMED.name().toLowerCase())) {
                                    business.setStatus(StatusType.REJECTED.name().toLowerCase());
                                    BaseDao.updateDocument(business);
                                    BaseDao.insertLog(user, business, LogType.UPDATE);

                                    // mando mail reject
                                    User businessOwner = BaseDao.getDocumentById(business.getUserId(), User.class);
                                    if (businessOwner != null && StringUtils.isNotBlank(businessOwner.getEmail())) {
                                        Map<String, Object> mailParams = new HashMap<>();
                                        mailParams.put("type", "ACCOUNT_ACTIVITY_REFUSED");
                                        mailParams.put("language", language);
                                        mailParams.put("to", StringUtils.defaultIfBlank(businessOwner.getEmail(), ""));
                                        // Add user object for template access to user.keyword and user.registrationToken
                                        mailParams.put("user", user);
                                        mailParams.put("business", business);
                                        // Add baseUrl for template links
                                        String baseUrl = RoutesUtils.baseUrl(request);
                                        mailParams.put("baseUrl", baseUrl);
                                        NotificationCommons.sendTemplatedEmail(mailParams, false, false);
                                    }
                                    break;
                                }
                            case "preferred":
                                if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.CONFIRMED.name().toLowerCase())) {
                                    business.setEditorChoice(true);
                                    BaseDao.updateDocument(business);
                                    BaseDao.insertLog(user, business, LogType.UPDATE);
                                    break;
                                }
                            case "unpreferred":
                                if (StringUtils.equalsIgnoreCase(business.getStatus(), StatusType.CONFIRMED.name().toLowerCase())) {
                                    business.setEditorChoice(false);
                                    BaseDao.updateDocument(business);
                                    BaseDao.insertLog(user, business, LogType.UPDATE);
                                    break;
                                }
                        }
                    }
                }
            }
        }

        return "ok";
    };

    public static Route business_preferred_toggle = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.CUSTOMER);

        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);

        String businessIdParam = params.get("businessId");
        if (StringUtils.isBlank(businessIdParam)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Business ID is required");
        }

        ObjectId businessId = RequestUtils.toObjectId(businessIdParam);
        ObjectId userId = user.getId();

        if (businessId == null || userId == null) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Invalid user or business ID");
        }

        try {
            // Create QueryOptions to find existing BusinessPreferred record
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
            filters.add(DaoFilters.getFilter("businessId", DaoFiltersOperation.EQ, businessId));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 1, null, null);

            // Check if BusinessPreferred record exists
            BusinessPreferred existingPreferred = BaseDao.getDocumentByFilters(BusinessPreferred.class, queryOptions);

            if (existingPreferred != null) {
                // Record exists, delete it (remove from preferred)
                BaseDao.deleteDocument(existingPreferred);
                BaseDao.insertLog(user, existingPreferred, LogType.DELETE);

                Business business = BaseDao.getDocumentById(businessId, Business.class);
                BusinessUtils.updateBusinessAnalytics(business, "followers");
                return "removed";
            } else {
                // Record doesn't exist, create it (add to preferred)
                BusinessPreferred newPreferred = new BusinessPreferred();
                newPreferred.setUserId(userId);
                newPreferred.setBusinessId(businessId);

                ObjectId insertedId = BaseDao.insertDocument(newPreferred);
                if (insertedId != null) {
                    BaseDao.insertLog(user, newPreferred, LogType.INSERT);

                    Business business = BaseDao.getDocumentById(businessId, Business.class);
                    BusinessUtils.updateBusinessAnalytics(business, "followers");
                    return "added";
                } else {
                    throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, "Failed to create preferred business record");
                }
            }
        } catch (Exception ex) {
            LOGGER.error("Error managing business preferred for userId: " + userId + ", businessId: " + businessId, ex);
            throw Spark.halt(HttpStatus.INTERNAL_SERVER_ERROR_500, "Internal server error");
        }
    };

    public static TemplateViewRoute business_detail = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        if (StringUtils.isBlank(identifier)) {
            throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Business identifier is required");
        }

        // business
        Business business = BaseDao.getDocumentByIdentifier(identifier, Business.class);
        if (business == null) {
            throw Spark.halt(HttpStatus.NOT_FOUND_404, "Business not found");
        }
        attributes.put("business", business);
        User businessOwner = BaseDao.getDocumentById(business.getUserId(), User.class);
        attributes.put("businessOwner", businessOwner);

        BusinessUtils.updateBusinessAnalytics(business, "view");

        if (user != null) {
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("userId", DaoFiltersOperation.EQ, user.getId()));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            List<BusinessPreferred> businessPreferreds = BaseDao.getDocumentsByFilters(BusinessPreferred.class, queryOptions, false);
            // Map them for id
            Map<ObjectId, BusinessPreferred> businessPreferredMap = new HashMap<>();
            for (BusinessPreferred businessPreferred : businessPreferreds) {
                businessPreferredMap.put(businessPreferred.getBusinessId(), businessPreferred);
            }
            attributes.put("businessPreferreds", businessPreferredMap);
        }        
        
        return Core.render(Pages.BUSINESS_DETAIL, attributes, request);
    };

    public static Route send_contact_mail = (Request request, Response response) -> {
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        String language = RoutesUtils.language(request);

        // TODO: salvare i files in file temporanei e poi cancellarli dopo aver inviato la mail
        try {
            String why = "Informazioni Generali";
            ObjectId businessId = RequestUtils.toObjectId(params.get("businessId"));
            if (businessId == null) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Business ID is required");
            }
            Business business = BaseDao.getDocumentById(businessId, Business.class);
            if (business == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, "Business not found");
            }
            User businessOwner = BaseDao.getDocumentById(business.getUserId(), User.class);
            if (businessOwner == null) {
                throw Spark.halt(HttpStatus.NOT_FOUND_404, "Business owner not found");
            }
            String email = businessOwner.getEmail();
            Map<String, Object> objectParams = new HashMap<>();
            for (String key : params.keySet()) {
                objectParams.put(key, params.get(key));
            }
            objectParams.put("type", "CONTACT");
            objectParams.put("to", business.getContactEmail());
            objectParams.put("language", language);
            objectParams.put("why", why);
            objectParams.put("buninessId", business.getId());

            // Add baseUrl for template links
            String baseUrl = RoutesUtils.baseUrl(request);
            objectParams.put("baseUrl", baseUrl);
            
            if (!params.containsKey("recaptchaToken")) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid recaptcha");
            }
            if (!GoogleRecaptchaValidator.isValid(params.get("recaptchaToken").toString())) {
                throw Spark.halt(HttpStatus.BAD_REQUEST_400, "invalid recaptcha");
            }

            NotificationCommons.sendTemplatedEmail(objectParams, false, false);
            BusinessUtils.updateBusinessAnalytics(business, "messages");
            
            objectParams = new HashMap<>();
            for (String key : params.keySet()) {
                objectParams.put(key, params.get(key));
            }
            objectParams.put("to", params.get("email"));
            objectParams.put("language", language);
            // Add baseUrl for template links
            objectParams.put("baseUrl", baseUrl);
            
            NotificationCommons.sendTemplatedEmail(objectParams, false, true);
        } catch (Exception ex) {
            LOGGER.error("Error sending email: ", ex);
            // Re-throw the exception to maintain the same error handling behavior
            throw ex;
        }

        return null;
    };

    private static Location geocode(Business business) {
        Location location = null;

        if (business != null) {
            if (StringUtils.isNotBlank(business.getCity())
                    && StringUtils.isNotBlank(business.getAddress())
                    && StringUtils.isNotBlank(business.getProvinceCode())
                    && StringUtils.isNotBlank(business.getZip())) {

                String address = business.getAddress() + ", " + business.getZip() + " " + business.getCity() + ", " + business.getProvinceCode();
                location = Geocoder.geocode(address);
            }
        }

        return location;
    }
}