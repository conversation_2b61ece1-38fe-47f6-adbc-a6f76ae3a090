package controller;

import com.github.slugify.Slugify;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import enums.StatusType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Event;
import pojo.Event;
import pojo.QueryOptions;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.*;

/**
 *
 * <AUTHOR>
 */
public class EventController {

    private static final Logger LOGGER = LoggerFactory.getLogger(EventController.class.getName());

    public static TemplateViewRoute be_event_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_EVENT_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_event = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
            attributes.put("requiredLanguage", language);
        }
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("eventId"));
        if (oid != null) {
            Event loadedEvent = BaseDao.getDocumentById(oid, Event.class, language);
            attributes.put("event", loadedEvent);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Event loadedEvent = BaseDao.getDocumentByParentId(parentId, Event.class, language);
                if (loadedEvent != null) {
                    attributes.put("event", loadedEvent);
                }
            }
        }

        return Core.render(Pages.BE_EVENT, attributes, request);
    };

    public static Route be_event_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Event> eventListToCheck = new ArrayList<>();
        Map<String, Event> parentEventMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Event> loadedEvents;
            if (loadArchived) {
                loadedEvents = BaseDao.getArchivedDocuments(Event.class, languageToLoad);
            } else {
                loadedEvents = BaseDao.getDocuments(Event.class, languageToLoad);
            }
            if (loadedEvents != null && !loadedEvents.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Event event : loadedEvents) {
                        parentEventMap.put(event.getParentId(), event);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    eventListToCheck.addAll(loadedEvents);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!eventListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Event event : eventListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, event.getLanguage())) {
                            if (!parentEventMap.containsKey(event.getParentId())) {
                                parentEventMap.put(event.getParentId(), event);
                            }
                        }
                    }
                }
            }
        }
        List<Event> eventList = new ArrayList<>(parentEventMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!eventList.isEmpty()) {
            for (Event event : eventList) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a language='").append(event.getLanguage()).append("' eventId='").append(event.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_EVENT + "?eventId=").append(event.getId()).append("&language=").append(event.getLanguage()).append("'>").append(event.getTitle()).append("</a>\",");
                json.append("\"").append(DateTimeUtils.dateToString(event.getEventDate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(event.getStatus() != null ? event.getStatus() : "").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(event.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(event.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_event_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("eventId"));
        Event newEvent;
        if (oid != null) {
            newEvent = BaseDao.getDocumentById(oid, Event.class, language);
            RequestUtils.mergeFromParams(params, newEvent);
        } else {
            newEvent = RequestUtils.createFromParams(params, Event.class);
            if (StringUtils.isBlank(newEvent.getIdentifier())) {
                newEvent.setIdentifier(newEvent.getTitle() + "-" + RoutesUtils.generateIdentifier());
            }
        }

        if (newEvent != null) {
            if (!params.containsKey("editorChoice")) {
                newEvent.setEditorChoice(false);
            }

            Event loadedByIdentifier = BaseDao.getDocumentByIdentifier(newEvent.getIdentifier(), Event.class, language);
            Event loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Event.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Event eventToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Event.class, languageToUpdate);
                        if (eventToUpdate != null) {
                            eventToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(eventToUpdate, eventToUpdate.getLanguage());
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage());
            }

            if (loadedByIdentifier != null && loadedByIdentifier.getId() != null) {
                if (newEvent.getId() == null || !loadedByIdentifier.getId().equals(newEvent.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Event with the same identifier already exists");
                }
            }

            newEvent.setTags(null);
            for (String param : params.keySet()) {
                // potrebbe essere items[0] se caricato dall'oggetto
                // oppure items_new[0] se arriva da input in pagina
                if (StringUtils.equals(param, "tags")) {
                    if (newEvent.getTags() == null) {
                        newEvent.setTags(new ArrayList<>());
                    }
                    List<String> splitted = Arrays.asList(StringUtils.split(params.get(param), "|"));
                    newEvent.setTags(splitted);
                }
            }
            newEvent.setLanguage(language);
            if (loadedByParentId != null) {
                newEvent.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newEvent.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newEvent.getParentId())) {
                newEvent.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newEvent.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newEvent, language);
                newEvent.setId(oid);

                BaseDao.insertLog(user, newEvent, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newEvent, language);
                BaseDao.insertLog(user, newEvent, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(eventFromQuery, newEvent);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newEvent, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newEvent, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newEvent != null ? newEvent.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_event_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String events = params.get("eventIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(events) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> eventIds = new HashMap<>();
            if (StringUtils.contains(events, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(events, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        eventIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(events, "|"));
                if (parts.size() == 2) {
                    eventIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!eventIds.isEmpty()) {
                for (ObjectId eventId : eventIds.keySet()) {
                    String language = eventIds.get(eventId);
                    Event eventToArchive;
                    if (isArchived) {
                        eventToArchive = BaseDao.getArchivedDocumentById(eventId, Event.class, language);
                    } else {
                        eventToArchive = BaseDao.getDocumentById(eventId, Event.class, language);
                    }
                    if (eventToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            eventToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            eventToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(eventToArchive, language);

                        List<String> languagesToUpdate = eventToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Event subEventToArchive = BaseDao.getDocumentByParentId(eventToArchive.getParentId(), Event.class, languageToUpdate);
                                    if (subEventToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subEventToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subEventToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subEventToArchive, languageToUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };

    public static TemplateViewRoute event_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        // Parse request parameters
        Map<String, String> params = new HashMap<>();
        Map<String, UploadedFile> files = new HashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String language = RoutesUtils.language(request);
        String category = null;
        List<String> tags = null;
        if (params.containsKey("category")) {
            category = params.get("category");
        }
        if (params.containsKey("tags")) {
            tags = Arrays.asList(StringUtils.split(params.get("tags"), ","));
        }

        // Build filters list
        List<Bson> filters = new ArrayList<>();

        // Always filter by confirmed status
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.PUBLISHED.name().toLowerCase()));
        filters.add(DaoFilters.getFilter("publication", DaoFiltersOperation.LTE, TimeUtils.today()));
        if (StringUtils.isNotBlank(category)) {
            filters.add(DaoFilters.getFilter("category", DaoFiltersOperation.EQ, category));
        }
        if (tags != null && !tags.isEmpty()) {
            filters.add(DaoFilters.getFilter("tags", DaoFiltersOperation.IN, tags));
        }

        // Pagination parameters
        int page = 1;
        int limit = 10; // Default items per page

        try {
            if (StringUtils.isNotBlank(params.get("page"))) {
                page = Integer.parseInt(params.get("page"));
            }
            if (StringUtils.isNotBlank(params.get("limit"))) {
                limit = Integer.parseInt(params.get("limit"));
            }
        } catch (NumberFormatException ex) {
            LOGGER.warn("Invalid pagination parameters", ex);
        }

        int skip = (page - 1) * limit;

        // Sorting parameters
        String orderBy = StringUtils.defaultIfBlank(params.get("orderBy"), "publication");
        String orderType = StringUtils.defaultIfBlank(params.get("orderType"), "desc");

        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, skip, limit, orderBy, orderType);

        try {
            // Execute query
            List<Event> events = BaseDao.getDocumentsByFilters(Event.class, queryOptions, language);
            long totalCount = BaseDao.countDocumentsByFilters(Event.class, queryOptions, language);

            // Add results to attributes
            attributes.put("events", events);
            attributes.put("totalCount", totalCount);
            attributes.put("currentPage", page);
            attributes.put("limit", limit);

            // Add filter parameters back to attributes for form state
            attributes.put("filters", params);
        } catch (Exception ex) {
            LOGGER.error("Error executing event query", ex);
            attributes.put("events", new ArrayList<Event>());
            attributes.put("totalCount", 0);
            attributes.put("error", "Unable to load results. Please try again.");
        }

        return Core.render(Pages.EVENT_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute event = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();

        Core.initializeRouteFrontEnd(request, response, attributes);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Event event = null;
        if (StringUtils.isNotBlank(identifier)) {
            event = BaseDao.getDocumentByIdentifier(identifier, Event.class, attributes.get("language").toString());
        }

        attributes.put("event", event);

        return Core.render(Pages.EVENT, attributes, request);
    }; 
}
