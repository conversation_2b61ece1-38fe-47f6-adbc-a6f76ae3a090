package controller;

import com.github.slugify.Slugify;
import core.Core;
import core.Pages;
import core.Routes;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.LogType;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import enums.StatusType;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.bson.types.ObjectId;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.*;
import pojo.Project;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.*;

/**
 *
 * <AUTHOR>
 */
public class ProjectController {

    private static final Logger LOGGER = LoggerFactory.getLogger(ProjectController.class.getName());

    public static TemplateViewRoute be_project_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // non serve nulla perchè i dati vengono caricati tramite ajax
        return Core.render(Pages.BE_PROJECT_COLLECTION, attributes, request);
    };

    public static TemplateViewRoute be_project = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        // logged user
        String language = user.getLanguage();
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
            attributes.put("requiredLanguage", language);
        }
        if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
            attributes.put("parentId", params.get("parentId"));
            attributes.put("parentIdLanguage", params.get("parentIdLanguage"));
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("projectId"));
        if (oid != null) {
            Project loadedProject = BaseDao.getDocumentById(oid, Project.class, language);
            attributes.put("project", loadedProject);
        } else {
            String parentId = request.queryParams("parentId");
            if (StringUtils.isNotBlank(parentId)) {
                Project loadedProject = BaseDao.getDocumentByParentId(parentId, Project.class, language);
                if (loadedProject != null) {
                    attributes.put("project", loadedProject);
                }
            }
        }

        return Core.render(Pages.BE_PROJECT, attributes, request);
    };

    public static Route be_project_data = (Request request, Response response) -> {
        // logged user
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, null);
        if (params.containsKey("language")) {
            language = params.get("language");
        }
        List<String> languages = new ArrayList<>();
        if (params.containsKey("languages")) {
            languages = Arrays.asList(StringUtils.split(params.get("languages"), "|"));
        } else {
            languages.add(language);
        }
        boolean loadArchived = false;
        if (params.containsKey("archived")) {
            loadArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("archived")));
        }

        List<Project> projectListToCheck = new ArrayList<>();
        Map<String, Project> parentProjectMap = new LinkedHashMap<>();
        for (String languageToLoad : languages) {
            List<Project> loadedProjects;
            // Create query options
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(new ArrayList<>(), 0, 0, "creation", "desc");
            if (loadArchived) {
                loadedProjects = BaseDao.getDocumentsByFilters(Project.class, queryOptions, languageToLoad, true);
            } else {
                loadedProjects = BaseDao.getDocumentsByFilters(Project.class, queryOptions, languageToLoad, false);
            }
            if (loadedProjects != null && !loadedProjects.isEmpty()) {
                if (StringUtils.equals(language, languageToLoad)) { // se sto caricando la lingua dell'utente sono tutte valide righe
                    for (Project project : loadedProjects) {
                        parentProjectMap.put(project.getParentId(), project);
                    }
                } else { // altrimenti aggiungo alla lista di quelle da decidere quale lingua tenere
                    projectListToCheck.addAll(loadedProjects);
                }
            }
        }
        if (languages.size() > 1) {
            List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);
            // tolgo lingue che non ho richiesto e quella dell'utente
            availableLanguages.retainAll(languages);
            availableLanguages.remove(language);
            // ora di tutte gli altri articoli li carico in ordine alle lingue definite nel sito
            if (!projectListToCheck.isEmpty()) {
                for (String languageToLoad : availableLanguages) {
                    for (Project project : projectListToCheck) {
                        if (StringUtils.equalsIgnoreCase(languageToLoad, project.getLanguage())) {
                            if (!parentProjectMap.containsKey(project.getParentId())) {
                                parentProjectMap.put(project.getParentId(), project);
                            }
                        }
                    }
                }
            }
        }
        List<Project> projectList = new ArrayList<>(parentProjectMap.values());

        StringBuilder json = new StringBuilder("{ \"data\": [");
        if (!projectList.isEmpty()) {
            for (Project project : projectList) {
                json.append("[");
                json.append("\"").append("\","); // prima colonna vuota
                json.append("\"<a language='").append(project.getLanguage()).append("' projectId='").append(project.getId()).append("' href='").append(RoutesUtils.contextPath(request)).append(Routes.BE_PROJECT + "?projectId=").append(project.getId()).append("&language=").append(project.getLanguage()).append("'>").append(project.getTitle()).append("</a>\",");
                json.append("\"").append(project.getCategory() != null ? project.getCategory() : "").append("\",");
                json.append("\"").append(project.getStatus() != null ? project.getStatus() : "").append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(project.getPublication(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(StringUtils.join(project.getAvailableLanguages(), ", ")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(project.getCreation(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append(DateTimeUtils.dateToString(project.getLastUpdate(), "dd/MM/YYYY")).append("\",");
                json.append("\"").append("Azioni").append("\",");
                json.append("\"").append("\""); // ultima colonna vuota
                json.append("],");
            }
            json.deleteCharAt(json.length() - 1); // rimuovo ultima virgola array
        }
        json.append("]}");

        return json.toString();
    };

    public static Route be_project_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        String language = user != null ? user.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE;
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);
        if (params.containsKey("language")) {
            language = params.get("language");
        }

        ObjectId oid = RequestUtils.toObjectId(request.queryParams("projectId"));
        Project newProject;
        if (oid != null) {
            newProject = BaseDao.getDocumentById(oid, Project.class, language);
            if (StringUtils.isBlank(newProject.getCategoryIdentifier())) {
                Slugify slg = new Slugify();
                String categoryIdentifier = newProject.getCategory();
                newProject.setCategoryIdentifier(slg.slugify(categoryIdentifier));
            } 
            RequestUtils.mergeFromParams(params, newProject);
        } else {
            newProject = RequestUtils.createFromParams(params, Project.class);
            if (StringUtils.isBlank(newProject.getIdentifier())) {
                newProject.setIdentifier(newProject.getTitle() + "-" + RoutesUtils.generateIdentifier());
            }
            if (StringUtils.isBlank(newProject.getCategoryIdentifier())) {
                Slugify slg = new Slugify();
                String categoryIdentifier = newProject.getCategory();
                newProject.setCategoryIdentifier(slg.slugify(categoryIdentifier));
            }            
        }

        if (newProject != null) {
            if (!params.containsKey("editorChoice")) {
                newProject.setEditorChoice(false);
            }

            Project loadedByIdentifier = BaseDao.getDocumentByIdentifier(newProject.getIdentifier(), Project.class, language);
            Project loadedByParentId = null;
            if (params.containsKey("parentId") && params.containsKey("parentIdLanguage")) {
                loadedByParentId = BaseDao.getDocumentByParentId(params.get("parentId"), Project.class, params.get("parentIdLanguage"));
            }
            if (loadedByParentId != null && oid == null) {
                // se ho trovato l'articolo padre e sono in inserimento aggiungo la lingua
                List<String> langs = new ArrayList<>(loadedByParentId.getAvailableLanguages());
                langs.add(language);
                loadedByParentId.setAvailableLanguages(langs);
                
                List<String> languageToNotUpdate = new ArrayList<>(Arrays.asList(loadedByParentId.getLanguage(), language));
                List<String> languageAvailable = new ArrayList<>(loadedByParentId.getAvailableLanguages()); // devo fare clone perchè ora tolgo elementi
                languageAvailable.removeAll(languageToNotUpdate);
                if (!languageAvailable.isEmpty()) {
                    // se ci sono altre lingue da aggiornare
                    for (String languageToUpdate : languageAvailable) {
                        Project projectToUpdate = BaseDao.getDocumentByParentId(loadedByParentId.getParentId(), Project.class, languageToUpdate);
                        if (projectToUpdate != null) {
                            projectToUpdate.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                            BaseDao.updateDocument(projectToUpdate, projectToUpdate.getLanguage());
                        }
                    }
                }

                BaseDao.updateDocument(loadedByParentId, loadedByParentId.getLanguage());
            }

            if (loadedByIdentifier != null && loadedByIdentifier.getId() != null) {
                if (newProject.getId() == null || !loadedByIdentifier.getId().equals(newProject.getId())) {
                    throw Spark.halt(HttpStatus.BAD_REQUEST_400, "Project with the same identifier already exists");
                }
            }

            newProject.setTags(null);
            for (String param : params.keySet()) {
                // potrebbe essere items[0] se caricato dall'oggetto
                // oppure items_new[0] se arriva da input in pagina
                if (StringUtils.equals(param, "tags")) {
                    if (newProject.getTags() == null) {
                        newProject.setTags(new ArrayList<>());
                    }
                    List<String> splitted = Arrays.asList(StringUtils.split(params.get(param), "|"));
                    newProject.setTags(splitted);
                }
            }
            newProject.setLanguage(language);
            if (loadedByParentId != null) {
                newProject.setAvailableLanguages(loadedByParentId.getAvailableLanguages());
                newProject.setParentId(loadedByParentId.getParentId());
            } else if (StringUtils.isBlank(newProject.getParentId())) {
                newProject.setParentId(UUID.randomUUID().toString());
                List<String> availableLanguages = new ArrayList<>(Arrays.asList(language));
                newProject.setAvailableLanguages(availableLanguages);
            }

            if (oid == null) {
                oid = BaseDao.insertDocument(newProject, language);
                newProject.setId(oid);

                BaseDao.insertLog(user, newProject, LogType.INSERT);
            } else {
                BaseDao.updateDocument(newProject, language);
                BaseDao.insertLog(user, newProject, LogType.UPDATE);

//                try {
//                    List<ObjectDifference> differences = ObjectUtils.getDifference(projectFromQuery, newProject);
//                    System.out.println("ok");
//                } catch (Exception ex) {
//                    LOGGER.error("Error on getDifference", ex);
//                }
            }

            if (!files.isEmpty()) {
                BaseDao.deleteImages(newProject, "imageIds");
                BaseDao.saveImages(new ArrayList<>(files.values()), newProject, "imageIds");
            }
        }

        // se errore ritorno Spark.halt()
        return oid + "&language=" + (newProject != null ? newProject.getLanguage() : Defaults.DEFAULT_USER_LANGUAGE);
    };

    public static Route be_project_operate = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        String projects = params.get("projectIds");
        String operation = params.get("operation");
        Boolean isArchived = BooleanUtils.isTrue(BooleanUtils.toBoolean(params.get("fromArchived")));
        if (StringUtils.isNotBlank(projects) && StringUtils.isNotBlank(operation)) {
            Map<ObjectId, String> projectIds = new HashMap<>();
            if (StringUtils.contains(projects, ",")) {
                List<String> ids = Arrays.asList(StringUtils.split(projects, ","));
                for (String id : ids) {
                    List<String> parts = Arrays.asList(StringUtils.split(id, "|"));
                    if (parts.size() == 2) {
                        projectIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                    }
                }
            } else {
                List<String> parts = Arrays.asList(StringUtils.split(projects, "|"));
                if (parts.size() == 2) {
                    projectIds.put(RequestUtils.toObjectId(parts.get(0)), parts.get(1));
                }
            }

            if (!projectIds.isEmpty()) {
                for (ObjectId projectId : projectIds.keySet()) {
                    String language = projectIds.get(projectId);
                    Project projectToArchive;
                    if (isArchived) {
                        projectToArchive = BaseDao.getArchivedDocumentById(projectId, Project.class, language);
                    } else {
                        projectToArchive = BaseDao.getDocumentById(projectId, Project.class, language);
                    }
                    if (projectToArchive != null) {
                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                            projectToArchive.setArchived(true);
                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                            projectToArchive.setCancelled(true);
                        }
                        BaseDao.updateDocument(projectToArchive, language);

                        List<String> languagesToUpdate = projectToArchive.getAvailableLanguages();
                        if (languagesToUpdate != null && !languagesToUpdate.isEmpty()) {
                            for (String languageToUpdate : languagesToUpdate) {
                                if (!StringUtils.equals(language, languageToUpdate)) { // non aggiorno quella base appena aggiornata
                                    Project subProjectToArchive = BaseDao.getDocumentByParentId(projectToArchive.getParentId(), Project.class, languageToUpdate);
                                    if (subProjectToArchive != null) {
                                        if (StringUtils.equalsIgnoreCase(operation, "archive")) {
                                            subProjectToArchive.setArchived(true);
                                        } else if (StringUtils.equalsIgnoreCase(operation, "delete")) {
                                            subProjectToArchive.setCancelled(true);
                                        }
                                        BaseDao.updateDocument(subProjectToArchive, languageToUpdate);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        return "ok";
    };

    public static TemplateViewRoute project_collection = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);

        String language = RoutesUtils.language(request);
        String category = null;
        List<String> tags = null;
        if (request.params("category") != null) {
            category = request.params("category");
            attributes.put("categoryParam", category);
        }
        if (request.queryParams("tags") != null) {
            tags = Arrays.asList(StringUtils.split(request.queryParams("tags"), ","));
            attributes.put("tagsParam", tags);
        }

        // Build filters list
        List<Bson> filters = new ArrayList<>();

        // Always filter by confirmed status
        filters.add(DaoFilters.getFilter("status", DaoFiltersOperation.EQ, StatusType.PUBLISHED.name().toLowerCase()));
        filters.add(DaoFilters.getFilter("publication", DaoFiltersOperation.LTE, TimeUtils.today()));
        if (StringUtils.isNotBlank(category)) {
            filters.add(DaoFilters.getFilter("categoryIdentifier", DaoFiltersOperation.EQ, category));
        }
        if (tags != null && !tags.isEmpty()) {
            filters.add(DaoFilters.getFilter("tags", DaoFiltersOperation.IN, tags));
        }

        // Pagination parameters
        int page = 1;
        int limit = 10; // Default items per page

        try {
            if (StringUtils.isNotBlank(request.queryParams("page"))) {
                page = Integer.parseInt(request.queryParams("page"));
            }
            if (StringUtils.isNotBlank(request.queryParams("limit"))) {
                limit = Integer.parseInt(request.queryParams("limit"));
            }
        } catch (NumberFormatException ex) {
            LOGGER.warn("Invalid pagination parameters", ex);
        }

        int skip = (page - 1) * limit;

        // Sorting parameters
        String orderBy = StringUtils.defaultIfBlank(request.queryParams("orderBy"), "publication");
        String orderType = StringUtils.defaultIfBlank(request.queryParams("orderType"), "desc");

        // Create query options
        QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, skip, limit, orderBy, orderType);

        try {
            // Execute query
            List<Project> projects = BaseDao.getDocumentsByFilters(Project.class, queryOptions, language);
            long totalCount = BaseDao.countDocumentsByFilters(Project.class, queryOptions, language);

            // Add results to attributes
            attributes.put("projects", projects);
            attributes.put("totalCount", totalCount);
            attributes.put("currentPage", page);
            attributes.put("limit", limit);

        } catch (Exception ex) {
            LOGGER.error("Error executing project query", ex);
            attributes.put("projects", new ArrayList<Project>());
            attributes.put("totalCount", 0);
            attributes.put("error", "Unable to load results. Please try again.");
        }

        return Core.render(Pages.PROJECT_COLLECTION, attributes, request);
    };
    
    public static TemplateViewRoute project = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        Core.initializeRouteFrontEnd(request, response, attributes);
        String language = RoutesUtils.language(request);

        // identifier
        String identifier = request.params("identifier");
        attributes.put("identifier", identifier);

        Project project = null;
        if (StringUtils.isNotBlank(identifier)) {
            project = BaseDao.getDocumentByIdentifier(identifier, Project.class, language);
        }

        attributes.put("project", project);

        return Core.render(Pages.PROJECT, attributes, request);
    };  
}
