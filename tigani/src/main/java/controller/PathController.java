package controller;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import core.Application;
import core.Core;
import core.Pages;
import dao.BaseDao;
import enums.ProfileType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.eclipse.jetty.http.HttpStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.HandsonTable;
import pojo.Path;
import pojo.PathItem;
import pojo.User;
import spark.Request;
import spark.Response;
import spark.Route;
import spark.Spark;
import spark.TemplateViewRoute;
import utils.Defaults;
import utils.RequestUtils;
import utils.UploadedFile;

/**
 *
 * <AUTHOR>
 */
public class PathController {

    private static final Logger LOGGER = LoggerFactory.getLogger(PathController.class.getName());

    public static TemplateViewRoute be_paths = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        return Core.render(Pages.BE_PATHS, attributes, request);
    };

    public static Route be_paths_data = (Request request, Response response) -> {
        // attributes
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);

        List<String> availableLanguages = new ArrayList<>(Defaults.AVAILABLE_LANGUAGES);

        List<Path> paths = BaseDao.getDocuments(Path.class);

        JsonArray jsonArray = new JsonArray();

        for (Path path : paths) {
            JsonObject jsonObject = new JsonObject();
            jsonObject.addProperty("key", path.getKey());

            for (PathItem item : path.getItems()) {
                if (availableLanguages.contains(item.getLanguage())) {
                    jsonObject.addProperty(item.getLanguage(), item.getDescription());
                }
            }
            jsonArray.add(jsonObject);
        }

        // se non c'è nulla metto riga vuota altrimenti non posso fare nulla
        if (jsonArray.isEmpty()) {
            JsonObject jsonObject = new JsonObject();
            jsonArray.add(jsonObject);
        }
        return jsonArray.toString();

    };


    public static Route be_paths_save = (Request request, Response response) -> {
        Map<String, Object> attributes = new HashMap<>();
        User user = Core.initializeRoute(request, response, attributes, ProfileType.ADMIN);
        
        Map<String, String> params = new LinkedHashMap<>();
        Map<String, UploadedFile> files = new LinkedHashMap<>();
        RequestUtils.parseRequest(request, params, files);

        // parse table data
        String json = params.get("json");
        HandsonTable table = null;
        if (StringUtils.isNotBlank(json)) {
            table = Core.deserializeFromJson(json, HandsonTable.class);
        }

        if (table != null) {

            // convert paths
            List<Path> paths = null;
            if (table.getData() != null) {
                paths = new ArrayList<>();
                for (String[] row : table.getData()) {
                    if (row.length > 0) {

                        if (row[0] != null) {
                            Path path = new Path();
                            path.setKey(row[0]);
                            path.setItems(new ArrayList<>());

                            for (String language : Defaults.AVAILABLE_LANGUAGES) {
                                int index = Defaults.AVAILABLE_LANGUAGES.indexOf(language);
                                PathItem item = new PathItem();
                                item.setLanguage(language);
                                if (row.length >= (index + 1)) {
                                    item.setDescription(row[index + 1]);
                                }
                                path.getItems().add(item);
                            }

                            paths.add(path);
                        }
                    }
                }
            }

            // clear pre-existing paths
            BaseDao.deleteCollection(Path.class);
            BaseDao.insertDocuments(paths);
            Application.reloadApplication();
            // clear paths cache
//            PathFunction.clear();
        } else {
            Spark.halt(HttpStatus.BAD_REQUEST_400, "Paths not updated");
        }

        return "ok";
    };

}
