package utils;

import core.Routes;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public class Defaults {

    public static String PROJECT_NAME = "tigani";
    public static String GOOGLE_RECAPTCHA_SECRET = "inserirechiave";
    public static String GOOGLE_RECAPTCHA_SITE = "inserirechiave";

    public static String FIRST_PAGE = Routes.BE_USER_COLLECTION;
    public static String MANIFEST_NAME = "META-INF/MANIFEST.MF";

    // sezione lingue
    public static String LANGUAGE = "it";
    public static List<String> ALL_LANGUAGES = new ArrayList<>(Arrays.asList("it", "en", "de", "fr", "es"));
    public static List<String> AVAILABLE_LANGUAGES = new ArrayList<>(); // loaded on Core.java, #start()
    public static List<String> VISIBLE_LANGUAGES = new ArrayList<>(); // loaded on Core.java, #start()
    public static String DEFAULT_USER_LANGUAGE = "it";

    // collection names for initialization
    public static List<String> COLLECTION_NAMES = new ArrayList<>(Arrays.asList("path"));

    // extra modules
    public static Boolean ENABLE_QUERY_LOGS = true;             // salva a db chi fa inserimenti, update, delete

    public static String REDIS_HOSTNAME = "localhost";
    public static String USER_SESSION_COOKIE = Defaults.PROJECT_NAME + "_user_session";
    public static String REDIS_PREFIX = Defaults.PROJECT_NAME + "_resource";
    public static int SESSION_DURATION = 28800;
    public static final long STATIC_RESOURCE_EXPIRATION_TIME = 2592000L;
}
