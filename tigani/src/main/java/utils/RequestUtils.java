package utils;

import java.beans.PropertyDescriptor;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.beanutils.PropertyUtils;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.FileUploadException;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import spark.Request;
import spark.utils.IOUtils;

/**
 *
 * <AUTHOR>
 */
public class RequestUtils {

    private static final Logger LOGGER = LoggerFactory.getLogger(RequestUtils.class.getName());

    public static void parseRequest(Request request, Map<String, String> params, Map<String, UploadedFile> files) {
        if (request != null) {
            if (params != null) {
                if (StringUtils.startsWithIgnoreCase(request.contentType(), "multipart/form-data")) {

                    // MULTIPART
                    List<FileItem> fields = null;
                    try {
                        fields = new ServletFileUpload(new DiskFileItemFactory()).parseRequest(request.raw());
                    } catch (FileUploadException ex) {
                        LOGGER.error("suppressed", ex);
                    }

                    if (fields != null) {
                        // multipart fields parsing
                        int counter = 0;
                        for (FileItem field : fields) {
                            if (field.isFormField()) {

                                // posted field
                                String name = field.getFieldName();
                                String value = null;
                                try {
                                    value = field.getString("UTF-8");
                                } catch (UnsupportedEncodingException ex) {
                                    LOGGER.error("suppressed", ex);
                                }

                                // setting value
                                if (StringUtils.isNotBlank(params.get(name))) {
                                    // accumulating values for fields with the same name
                                    params.put(name, params.get(name) + "|" + value);
                                } else {
                                    params.put(name, value);
                                }

                            } else {
                                if (files != null) {
                                    // posted files (skip empty)
                                    if (field.getSize() > 0L) {

                                        String name = FilenameUtils.getName(field.getName());
                                        String extension = FilenameUtils.getExtension(field.getName());

                                        String id = field.getFieldName();
                                        if (files.containsKey(id)) {
                                            counter++;
                                            id += "-" + counter;
                                        }
                                        try (InputStream content = field.getInputStream()) {
                                            files.put(id, new UploadedFile(name, field.getContentType(), extension, IOUtils.toByteArray(content)));
                                        } catch (IOException ex) {
                                            LOGGER.error("unable to save file " + name, ex);
                                        }
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // STANDARD
                    for (String name : request.queryParams()) {
                        // posted field
                        String[] values = request.queryParamsValues(name);

                        // setting value
                        if (values != null) {
                            if (values.length == 1) {
                                params.put(name, values[0]);
                            } else {
                                for (String value : values) {
                                    if (StringUtils.isNotBlank(params.get(name))) {
                                        // accumulating values for fields with the same name
                                        params.put(name, params.get(name) + "|" + value);
                                    } else {
                                        params.put(name, value);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    public static <T> T createFromParams(Map<String, String> params, Class<T> objectClass) {
        T result = null;
        if (params != null) {
            try {
                result = objectClass.newInstance();
            } catch (InstantiationException | IllegalAccessException | RuntimeException ex) {
                LOGGER.error("suppressed", ex);
            }
            if (result != null) {
                result = mergeFromParams(params, result);
            }
        }
        return result;
    }
    
    public static <T> T mergeFromParams(Map<String, String> params, T object) {
        if (params != null) {
            if (object != null) {
                for (String name : params.keySet()) {
                    
                    // property descriptor
                    PropertyDescriptor descriptor = null;
                    try {
                        descriptor = PropertyUtils.getPropertyDescriptor(object, name);
                    } catch (IllegalAccessException | InvocationTargetException | NoSuchMethodException | RuntimeException ex) {
                        LOGGER.error("suppressed", ex);
                    }
                    
                    if (descriptor != null) {
                        
                        // param value
                        String value = params.get(name);
                        
                        // on objectId fields, nulling "-" values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                            value = (!StringUtils.equals(value, "-") && !StringUtils.equals(value, "")) ? value : null;
                        }
                        
                        // on date fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        
                        // on integer fields, nulling empty string values
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Integer")) {
                            value = StringUtils.isNotBlank(value) ? value : null;
                        }
                        
                        // on double fields, handling "," as "."
                        if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Double")) {
                            value = StringUtils.replace(value, ",", ".");
                        }
                        
                        // setting value from request param
                        if (value != null) {
                            // manual pre-conversion of objectId references
                            Object obj = value;
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId")) {
                                try {
                                    obj = new ObjectId(value);
                                } catch (Exception ex) {
                                    LOGGER.error("suppressed", ex);
                                }
                            }
                            // manual pre-conversion of date and datetime references
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "Date")) {
                                if (value.length() > 10) {
                                    Date datetime = DateTimeUtils.stringToDate(value, "dd/MM/yyyy HH:mm");
                                    if (datetime != null) {
                                        obj = datetime;
                                    }
                                } else {
                                    Date datetime = DateTimeUtils.stringToDate(value, "dd/MM/yyyy");
                                    if (datetime != null) {
                                        obj = datetime;
                                    }
                                }
                            }
                            // manual pre-conversion of string[] references
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "String[]")) {
                                if (StringUtils.equals(obj.getClass().getSimpleName(), "String")) {
                                    obj = StringUtils.split((String) obj, "|");
                                }
                            }
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "ObjectId[]")) {
                                if (StringUtils.equals(obj.getClass().getSimpleName(), "String")) {
                                    String[] objArray = StringUtils.split((String) obj, "|");
                                    List<ObjectId> objList = new ArrayList<>();
                                    for (String objInArray : objArray) {
                                        if (StringUtils.isNotEmpty(objInArray)) {
                                            objList.add(new ObjectId(value));
                                        } else {
                                            objList.add(null);
                                        }
                                    }
                                    obj = objList.toArray(new ObjectId[0]);
                                }
                            }
                            if (StringUtils.equals(descriptor.getPropertyType().getSimpleName(), "List")) {
                                if (StringUtils.equals(obj.getClass().getSimpleName(), "String")) {
                                    obj = Arrays.asList(StringUtils.split((String) obj, "|"));
                                }
                            }
                            try {
                                BeanUtils.setProperty(object, name, obj);
                            } catch (IllegalAccessException | InvocationTargetException | RuntimeException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        } else {
                            // workaround to avoid BeanUtils limitation
                            try {
                                descriptor.getWriteMethod().invoke(object, (Object) null);
                            } catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException ex) {
                                LOGGER.error("suppressed", ex);
                            }
                        }
                        
                    }
                }
            }
        }
        return object;
    }
    
    public static ObjectId toObjectId(String value) {
        return StringUtils.isNotEmpty(value) ? (new ObjectId(value)) : null;
    }
}
