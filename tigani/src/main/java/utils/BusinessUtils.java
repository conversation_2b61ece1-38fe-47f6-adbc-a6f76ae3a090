package utils;

import com.github.slugify.Slugify;
import dao.BaseDao;
import dao.DaoFilters;
import dao.DaoFiltersOperation;
import enums.StatusType;
import org.apache.commons.lang3.StringUtils;
import org.bson.conversions.Bson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Business;
import pojo.BusinessAnalytics;
import pojo.BusinessPreferred;
import pojo.QueryOptions;

import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.List;
import pojo.Contact;

/**
 * Utility class for Business-related operations
 * 
 * <AUTHOR>
 */
public class BusinessUtils {
    
    private static final Logger LOGGER = LoggerFactory.getLogger(BusinessUtils.class.getName());
    
    /**
     * Generates a unique identifier for a business based on its fullname.
     * If the business already has an identifier, it won't be changed.
     * If the business has a fullname but no identifier, it will create one using Slugify.
     * If another business exists with the same identifier, it will add 7 random numbers to make it unique.
     * 
     * @param business The business object to generate identifier for
     * @throws Exception if an error occurs during database operations
     */
    public static void generateUniqueIdentifier(Business business) throws Exception {
        // Only generate identifier if business has fullname and identifier is empty
        if (business == null || StringUtils.isBlank(business.getFullname()) || StringUtils.isNotBlank(business.getIdentifier())) {
            return;
        }
        
        // LOGGER.debug("Generating unique identifier for business with fullname: {}", business.getFullname());
        
        // Create slugified identifier from fullname
        Slugify slugify = new Slugify();
        String baseIdentifier = slugify.slugify(business.getFullname());
        
        if (StringUtils.isBlank(baseIdentifier)) {
            LOGGER.warn("Unable to create identifier from fullname: {}", business.getFullname());
            return;
        }
        
        String uniqueIdentifier = baseIdentifier;
        Business existingBusiness = null;
        
        // Check if identifier already exists and make it unique if necessary
        try {
            existingBusiness = BaseDao.getDocumentByIdentifier(uniqueIdentifier, Business.class);
        } catch (Exception ex) {
            LOGGER.error("Error checking for existing business with identifier: {}", uniqueIdentifier, ex);
            throw ex;
        }
        
        // If identifier exists and it's not the same business, add random numbers
        while (existingBusiness != null && (business.getId() == null || !existingBusiness.getId().equals(business.getId()))) {
            String randomSuffix = generateRandomNumbers(7);
            uniqueIdentifier = baseIdentifier + "-" + randomSuffix;
            
            LOGGER.debug("Identifier {} already exists, trying: {}", baseIdentifier, uniqueIdentifier);
            
            try {
                existingBusiness = BaseDao.getDocumentByIdentifier(uniqueIdentifier, Business.class);
            } catch (Exception ex) {
                LOGGER.error("Error checking for existing business with identifier: {}", uniqueIdentifier, ex);
                throw ex;
            }
        }
        
        business.setIdentifier(uniqueIdentifier);
        LOGGER.info("Generated unique identifier for business: {}", uniqueIdentifier);
    }
    
    /**
     * Generates a string of random numbers with the specified length
     * 
     * @param length The number of digits to generate
     * @return A string containing random numbers
     */
    private static String generateRandomNumbers(int length) {
        SecureRandom random = new SecureRandom();
        StringBuilder sb = new StringBuilder();
        
        for (int i = 0; i < length; i++) {
            sb.append(random.nextInt(10));
        }
        
        return sb.toString();
    }

    public static void updateBusinessAnalytics(Business business) throws Exception {
        updateBusinessAnalytics(business, null);
    }

    public static void updateBusinessAnalytics(Business business, String what) throws Exception {
        if (business != null) {
            // check if business analytics exists
            List<Bson> filters = new ArrayList<>();
            filters.add(DaoFilters.getFilter("businessId", DaoFiltersOperation.EQ, business.getId()));
            QueryOptions queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
            BusinessAnalytics businessAnalytics = BaseDao.getDocumentByFilters(BusinessAnalytics.class, queryOptions);
            if (businessAnalytics == null) {
                businessAnalytics = new BusinessAnalytics();
                businessAnalytics.setBusinessId(business.getId());
                businessAnalytics.setViews(0);
                businessAnalytics.setFollowers(0);
                businessAnalytics.setMessages(0);
                businessAnalytics.setMessagesNotRead(0);
                businessAnalytics.setId(BaseDao.insertDocument(businessAnalytics));
            }

            if (StringUtils.isBlank(what) || StringUtils.equalsIgnoreCase(what, "view")) {
                if (businessAnalytics.getViews() == null) {
                    businessAnalytics.setViews(0);
                }
                businessAnalytics.setViews(businessAnalytics.getViews() + 1);
            }
            if (StringUtils.isBlank(what) || StringUtils.equalsIgnoreCase(what, "messages")) {
                if (businessAnalytics.getMessages() == null) {
                    businessAnalytics.setMessages(0);
                }
                int messages = BaseDao.countDocumentsByFilters(Contact.class, queryOptions).intValue();
                businessAnalytics.setMessages(messages);
                
                filters.add(DaoFilters.getFilter("read", DaoFiltersOperation.NE, true));
                queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
                int messagesNotRead = BaseDao.countDocumentsByFilters(Contact.class, queryOptions).intValue();
                if (businessAnalytics.getMessagesNotRead() == null) {
                    businessAnalytics.setMessagesNotRead(0);
                }
                businessAnalytics.setMessagesNotRead(messagesNotRead);
                
            }
            if (StringUtils.isBlank(what) || StringUtils.equalsIgnoreCase(what, "followers")) {
                int followers = BaseDao.countDocumentsByFilters(BusinessPreferred.class, queryOptions).intValue();
                businessAnalytics.setFollowers(followers);
            }
            if (StringUtils.isBlank(what) || StringUtils.equalsIgnoreCase(what, "messagesNotRead")) {
                filters.add(DaoFilters.getFilter("read", DaoFiltersOperation.NE, true));
                queryOptions = DaoFilters.createQueryWithOptions(filters, 0, 0, null, null);
                int messagesNotRead = BaseDao.countDocumentsByFilters(Contact.class, queryOptions).intValue();
                businessAnalytics.setMessagesNotRead(messagesNotRead);
            }

            BaseDao.updateDocument(businessAnalytics);
        }
    }
}
