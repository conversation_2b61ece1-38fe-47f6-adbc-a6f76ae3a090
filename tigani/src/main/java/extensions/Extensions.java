package extensions;

/**
 *
 * <AUTHOR>
 */

import com.mitchellbosecke.pebble.extension.AbstractExtension;
import com.mitchellbosecke.pebble.extension.Filter;
import com.mitchellbosecke.pebble.extension.Function;
import java.util.HashMap;
import java.util.Map;

public class Extensions extends AbstractExtension {

    @Override
    public Map<String, Filter> getFilters() {
        Map<String, Filter> filters = new HashMap<>();

        filters.put("removeHtmlTags", new RemoveHtmlTagsFilter());

        return filters;
    }

    @Override
    public Map<String, Function> getFunctions() {
        Map<String, Function> functions = new HashMap<>();

        functions.put("labels", new LabelsFunction());
        functions.put("routes", new RoutesFunction());
        functions.put("blob", new BlobFunction());
        functions.put("lookup", new LookupFunction());
        functions.put("count", new CountFunction());

        functions.put("get", new GetFunction());
        functions.put("next", new NextFunction());
        functions.put("prev", new PrevFunction());

        // lookup
        functions.put("taglist", new TagListFunction());
        functions.put("categorylist", new CategoryListFunction());

        return functions;
    }
}
