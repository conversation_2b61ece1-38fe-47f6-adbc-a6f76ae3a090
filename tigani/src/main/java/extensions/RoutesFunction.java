package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import core.Routes;
import dao.BaseDao;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Path;
import pojo.PathItem;
import utils.Defaults;
import utils.EnvironmentUtils;

/**
 *
 * <AUTHOR>
 */
public class RoutesFunction implements Function {

    private static final Logger LOGGER = LoggerFactory.getLogger(RoutesFunction.class.getName());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("key");
        names.add("language");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String key = (String) args.get("key");
        String language = (String) args.get("language");

        // contextPath
        String contextPath = (String) context.getVariable("contextPath");

        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }

        String localizedKey = key;
        if (Defaults.AVAILABLE_LANGUAGES.size() > 1 && !StringUtils.startsWithIgnoreCase(key, "BE_")) {
            localizedKey += "_" + language;
        }
        localizedKey = StringUtils.upperCase(localizedKey);

        Map<String, String> cachePaths = paths();
        if (cachePaths != null) {
            String path = "";
            if (EnvironmentUtils.isLocal() || EnvironmentUtils.hasNotDomainRedirectFromHost((String) context.getVariable("host"))) {
                if (StringUtils.isNotBlank(contextPath)) {
                    path += contextPath;
                }
            }
            path += paths().get(localizedKey);
            return path;
        } else {
            return "";
        }
    }

    private static Map<String, String> paths;

    private static Map<String, String> paths() {
        if (paths == null) {
            paths = Routes.getPaths();
            try {
                List<Path> databasePath = BaseDao.getDocuments(Path.class);

                if (databasePath != null && !databasePath.isEmpty()) {
                    for (Path path : databasePath) {
                        if (path.getItems() != null && !path.getItems().isEmpty()) {
                            for (PathItem labelInLanguage : path.getItems()) {
                                if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
                                    // niente prefisso della lingua
                                    if (path.getItems().size() == 1) {
                                        paths.put(path.getKey(), labelInLanguage.getDescription());
                                    }
                                } else {
                                    paths.put(StringUtils.upperCase(path.getKey() + "_" + labelInLanguage.getLanguage()), labelInLanguage.getDescription());
                                }
                            }
                        }
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("unable to load labels", ex);
            }
        }

        return paths;
    }
}
