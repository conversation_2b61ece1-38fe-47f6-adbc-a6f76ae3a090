package extensions;

import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.StringUtils;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *
 * <AUTHOR>
 */
public class GetFunction implements Function {
    
    private final Logger logger = LoggerFactory.getLogger(this.getClass());
    
    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("table");
        names.add("id");
        return names;        
    }
    
    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {
        
        String table = (String) args.get("table");
        ObjectId id = (ObjectId) args.get("id");
        
        if (StringUtils.isBlank(table)) {
            logger.error("missing table");
            return null;
        }
        if (id == null) {
            logger.error("missing id");
            return null;
        }
        
        return get(table, id);
    }
    
    private Object get(String table, ObjectId id) {
        Object obj = null;
        
        try {
//            obj = BaseDao.getDocumentById(table, id);
        } catch (Exception ex) {
            logger.error("suppressed", ex);
        }
        
        return obj;
    }
    
}
