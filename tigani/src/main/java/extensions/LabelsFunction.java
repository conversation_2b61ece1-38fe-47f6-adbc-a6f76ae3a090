package extensions;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.mitchellbosecke.pebble.extension.Function;
import com.mitchellbosecke.pebble.template.EvaluationContext;
import com.mitchellbosecke.pebble.template.PebbleTemplate;
import core.Core;
import dao.BaseDao;

import java.io.File;
import java.net.URL;
import java.util.*;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import pojo.Label;
import pojo.LabelItem;
import utils.Defaults;

/**
 *
 * <AUTHOR>
 */
public class LabelsFunction implements Function {

    private static final Logger LOGGER = LoggerFactory.getLogger(LabelsFunction.class.getName());

    @Override
    public List<String> getArgumentNames() {
        List<String> names = new ArrayList<>();
        names.add("key");
        names.add("language");
        return names;
    }

    @Override
    public Object execute(Map<String, Object> args, PebbleTemplate self, EvaluationContext context, int lineNumber) {

        String key = (String) args.get("key");
        String language = (String) args.get("language");

        // language
        if (StringUtils.isBlank(language)) {
            language = (String) context.getVariable("language");
        }

        String localizedKey = key;
        if (Defaults.AVAILABLE_LANGUAGES.size() > 1) {
            localizedKey += "_" + language;
        }
        localizedKey = StringUtils.upperCase(localizedKey);

        Map<String, String> cacheLabels = labels();
        if (cacheLabels != null) {
            String label = labels().get(localizedKey);
            return label;
        } else {
            return "";
        }
    }

    private static Map<String, String> labels;

    private static Map<String, String> labels() {
        if (labels == null) {
            labels = new HashMap<>();
            try {
                List<Label> databaseLabel = BaseDao.getDocuments(Label.class);
                if (databaseLabel != null && !databaseLabel.isEmpty()) {
                    elaborateLabels(databaseLabel, labels);
                }

                // leggo le label dal progetto
                URL url = LabelsFunction.class.getClassLoader().getResource("message/messages.json");
                if (url != null) {
                    String content = FileUtils.readFileToString(new File(url.getFile()), "UTF-8");
                    List<Label> systemLabels = Core.deserializeListFromJson(content, Label.class);
                    if (systemLabels != null && !systemLabels.isEmpty()) {
                        elaborateLabels(systemLabels, labels);
                    }
                }
            } catch (Exception ex) {
                LOGGER.error("unable to load labels", ex);
            }
        }

        return labels;
    }

    private static void elaborateLabels(List<Label> labelsToCheck, Map<String, String> labels) {
        for (Label label : labelsToCheck) {
            if (label.getItems() != null && !label.getItems().isEmpty()) {
                for (LabelItem labelInLanguage : label.getItems()) {
                    if (Defaults.AVAILABLE_LANGUAGES.size() == 1) {
                        // niente prefisso della lingua
                        if (label.getItems().size() == 1) {
                            labels.put(label.getKey(), labelInLanguage.getDescription());
                        }
                    } else {
                        labels.put(StringUtils.upperCase(label.getKey() + "_" + labelInLanguage.getLanguage()), labelInLanguage.getDescription());
                    }
                }
            }
        }
    }

    public static void loadLabels() {
        labels();
    }

    public static String description(String language, String key) {
        String localizedKey = key;
        if (Defaults.AVAILABLE_LANGUAGES.size() > 1) {
            localizedKey += "_" + language;
        }
        localizedKey = StringUtils.upperCase(localizedKey);

        Map<String, String> cacheLabels = labels();
        if (cacheLabels != null) {
            String label = labels().get(localizedKey);
            return label;
        } else {
            return "";
        }
        
    }
}
