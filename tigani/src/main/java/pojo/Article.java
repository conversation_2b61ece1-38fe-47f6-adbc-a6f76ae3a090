package pojo;

import java.util.Date;
import java.util.List;
import org.bson.types.ObjectId;

/**
 *
 * <AUTHOR>
 */
public class Article extends BasePojo implements Cloneable {

    // Main
    private String title;
    private String description;
    private String category;
    private String categoryIdentifier;

    // SEO
    private List<ObjectId> imageIds;
    private List<String> tags;
    private Boolean editorChoice;

    // Publication   
    private Date publication;
    private String identifier;              // slugify title
    private String visibility;              // public, private
    private String status;                  // draft, planned, published

    @Override
    public Article clone() throws CloneNotSupportedException {
        return (Article) super.clone();
    }
    
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategoryIdentifier() {
        return categoryIdentifier;
    }

    public void setCategoryIdentifier(String categoryIdentifier) {
        this.categoryIdentifier = categoryIdentifier;
    }

    public List<ObjectId> getImageIds() {
        return imageIds;
    }
    public void setImageIds(List<ObjectId> imageIds) {
        this.imageIds = imageIds;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Boolean getEditorChoice() {
        return editorChoice;
    }
    public void setEditorChoice(Boolean editorChoice) {
        this.editorChoice = editorChoice;
    }

    public Date getPublication() {
        return publication;
    }

    public void setPublication(Date publication) {
        this.publication = publication;
    }

    public String getIdentifier() {
        return identifier;
    }

    public void setIdentifier(String identifier) {
        this.identifier = identifier;
    }

    public String getVisibility() {
        return visibility;
    }

    public void setVisibility(String visibility) {
        this.visibility = visibility;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    
}
