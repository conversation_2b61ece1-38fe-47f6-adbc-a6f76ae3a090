package pojo;

import org.bson.types.ObjectId;

public class BusinessAnalytics extends BasePojo {

    private ObjectId businessId;
    private Integer views, followers, messages, messagesNotRead;

    public ObjectId getBusinessId() {
        return businessId;
    }

    public void setBusinessId(ObjectId businessId) {
        this.businessId = businessId;
    }

    public Integer getViews() {
        return views;
    }

    public void setViews(Integer views) {
        this.views = views;
    }

    public Integer getFollowers() {
        return followers;
    }

    public void setFollowers(Integer followers) {
        this.followers = followers;
    }

    public Integer getMessages() {
        return messages;
    }

    public void setMessages(Integer messages) {
        this.messages = messages;
    }

    public Integer getMessagesNotRead() {
        return messagesNotRead;
    }

    public void setMessagesNotRead(Integer messagesNotRead) {
        this.messagesNotRead = messagesNotRead;
    }
    
}
