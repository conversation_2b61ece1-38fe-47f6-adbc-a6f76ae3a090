// Global helpers
// ------------------------------

// Setting arrays
var appRoutes = new Map();
function addRoute(name, url) {
    appRoutes.set(name, url);
}

var appSettings = new Map();
function addSetting(name, value) {
    appSettings.set(name, value);
}

var pageVariables = new Map();
function addVariables(name, value) {
    pageVariables.set(name, value);
}

// Date format
moment.locale('it');


// Block UI
$.blockUI.defaults = {
    message: '<div class="blockui-loading">',
    css: {
        padding: 0,
        margin: 0,
        top: '45%',
        left: '50%',
        right: '50%',
        border: 'none',
        backgroundColor: 'transparent',
        cursor: 'wait'
    },
    overlayCSS: {
        backgroundColor: '#000',
        opacity: 0.4,
        cursor: 'wait'
    },
    baseZ: 1100,
    showOverlay: true
};

// Cancel button
document.addEventListener('DOMContentLoaded', (event) => {
    if (document.querySelector('.btn-cancel') !== null) {
        document.querySelector('.btn-cancel').addEventListener('click', function (e) {
            e.preventDefault();
            var href = this.getAttribute('href');

            // Mostra SweetAlert
            Swal.fire({
                title: 'Sei sicuro?',
                text: "Stai per uscire senza salvare le modifiche.",
                icon: 'warning',
                showCancelButton: true,
                reverseButtons: true,
                customClass: {
                    cancelButton: "btn btn-light",
                    confirmButton: "btn btn-primary"
                },
                buttonsStyling: false,
                cancelButtonText: 'No, resta qui',
                confirmButtonText: 'Sì, esci senza salvare'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Se l'utente conferma, reindirizza all'href originale del pulsante
                    window.location.href = href;
                }
            });
        });
    }
});
