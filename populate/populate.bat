@echo off

:: check for mongoimport presence
if not exist c:\opt\mongodb\server\bin\mongoimport.exe (
	echo ERROR: unable to find mongoimport tool
	goto :end
)


:: check for mongo presence
if not exist c:\opt\mongodb\server\bin\mongo.exe (
	echo ERROR: unable to find mongo tool
	goto :end
)


:: population
c:\opt\mongodb\server\bin\mongoimport.exe --db tigani --legacy --collection user --drop --file \projects\tigani\populate\user.json

c:\opt\mongodb\server\bin\mongoimport.exe --db tigani --legacy --collection company --drop --file \projects\tigani\populate\company.json
c:\opt\mongodb\server\bin\mongoimport.exe --db tigani --legacy --collection settings --drop --file \projects\tigani\populate\settings.json


	if errorlevel 1 (
		echo ERROR: unable to update password on user collection
		goto :end
	)


:: the end
:end
pause
